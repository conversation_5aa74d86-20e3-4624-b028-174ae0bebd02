import type { ChatCompletionInput, ChatCompletionStreamOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
/**
 * Use to continue text from a prompt. Same as `textGeneration` but returns generator that can be read one token at a time
 */
export declare function chatCompletionStream(args: BaseArgs & ChatCompletionInput, options?: Options): AsyncGenerator<ChatCompletionStreamOutput>;
//# sourceMappingURL=chatCompletionStream.d.ts.map