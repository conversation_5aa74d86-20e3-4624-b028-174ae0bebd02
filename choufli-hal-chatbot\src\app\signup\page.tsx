'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ThemeToggle from '../components/ThemeToggle';

export default function SignupPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      alert('Passwords do not match! / كلمات المرور غير متطابقة!');
      return;
    }
    
    setIsLoading(true);
    
    // Simulate signup process
    setTimeout(() => {
      setIsLoading(false);
      alert('Account created successfully! / تم إنشاء الحساب بنجاح!');
      router.push('/login');
    }, 2000);
  };

  return (
    <div className="min-h-screen choufli-background flex items-center justify-center p-4">
      <ThemeToggle />
      
      <div className="chat-container w-full max-w-md p-8 rounded-2xl shadow-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-glow mb-2" style={{ color: 'var(--choufli-primary)' }}>
            <span className="emoji-bounce">🎭</span> Choufli Hal
          </h1>
          <p className="text-lg font-medium" style={{ color: 'var(--foreground)' }}>
            Join our family! / انضم لعائلتنا!
          </p>
          <p className="text-sm mt-2 opacity-75" style={{ color: 'var(--foreground)' }}>
            Create your account to start chatting
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-2" style={{ color: 'var(--foreground)' }}>
              Full Name / الاسم الكامل
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="input-choufli w-full p-4 rounded-xl border-2 focus:outline-none focus:ring-4 focus:ring-opacity-50"
              placeholder="Ahmed Ben Ali"
              required
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-2" style={{ color: 'var(--foreground)' }}>
              Email / البريد الإلكتروني
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="input-choufli w-full p-4 rounded-xl border-2 focus:outline-none focus:ring-4 focus:ring-opacity-50"
              placeholder="<EMAIL>"
              required
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-2" style={{ color: 'var(--foreground)' }}>
              Password / كلمة المرور
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="input-choufli w-full p-4 rounded-xl border-2 focus:outline-none focus:ring-4 focus:ring-opacity-50"
              placeholder="••••••••"
              required
              minLength={6}
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
          </div>

          <div>
            <label htmlFor="confirmPassword" className="block text-sm font-medium mb-2" style={{ color: 'var(--foreground)' }}>
              Confirm Password / تأكيد كلمة المرور
            </label>
            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="input-choufli w-full p-4 rounded-xl border-2 focus:outline-none focus:ring-4 focus:ring-opacity-50"
              placeholder="••••••••"
              required
              minLength={6}
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full text-white py-4 px-6 rounded-xl font-bold text-lg btn-choufli shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
            }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="loading-dots">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
                <span>Creating account...</span>
              </div>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <span>Sign Up / تسجيل</span>
                <span>✨</span>
              </span>
            )}
          </button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-sm" style={{ color: 'var(--foreground)' }}>
            Already have an account? / لديك حساب بالفعل؟
          </p>
          <Link 
            href="/login" 
            className="text-lg font-medium hover:underline transition-colors duration-300"
            style={{ color: 'var(--choufli-accent)' }}
          >
            Sign in here / ادخل هنا
          </Link>
        </div>

        <div className="mt-6 text-center">
          <Link 
            href="/" 
            className="text-sm hover:underline transition-colors duration-300"
            style={{ color: 'var(--choufli-secondary)' }}
          >
            ← Back to Home / العودة للرئيسية
          </Link>
        </div>
      </div>

      {/* Decorative character */}
      <div
        className="choufli-character"
        title="Join the Choufli Hal family! 🎭"
        onClick={() => {
          const messages = [
            "أهلا وسهلا! Join our family!",
            "مرحبا بيك في العائلة!",
            "يا أهلا وسهلا! Welcome aboard!",
            "الحمد لله، عائلة جديدة!"
          ];
          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          alert(randomMessage);
        }}
      >
        👨‍👩‍👧‍👦
      </div>
    </div>
  );
}
