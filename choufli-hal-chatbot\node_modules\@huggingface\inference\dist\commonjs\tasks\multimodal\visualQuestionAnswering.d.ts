import type { VisualQuestionAnsweringInput, VisualQuestionAnsweringInputData, VisualQuestionAnsweringOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type VisualQuestionAnsweringArgs = BaseArgs & VisualQuestionAnsweringInput & {
    inputs: VisualQuestionAnsweringInputData & {
        image: Blob;
    };
};
/**
 * Answers a question on an image. Recommended model: dandelin/vilt-b32-finetuned-vqa.
 */
export declare function visualQuestionAnswering(args: VisualQuestionAnsweringArgs, options?: Options): Promise<VisualQuestionAnsweringOutput[number]>;
//# sourceMappingURL=visualQuestionAnswering.d.ts.map