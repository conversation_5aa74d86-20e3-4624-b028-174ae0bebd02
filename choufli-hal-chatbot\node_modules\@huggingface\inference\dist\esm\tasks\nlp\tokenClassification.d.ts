import type { TokenClassificationInput, TokenClassificationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type TokenClassificationArgs = BaseArgs & TokenClassificationInput;
/**
 * Usually used for sentence parsing, either grammatical, or Named Entity Recognition (NER) to understand keywords contained within text. Recommended model: dbmdz/bert-large-cased-finetuned-conll03-english
 */
export declare function tokenClassification(args: TokenClassificationArgs, options?: Options): Promise<TokenClassificationOutput>;
//# sourceMappingURL=tokenClassification.d.ts.map