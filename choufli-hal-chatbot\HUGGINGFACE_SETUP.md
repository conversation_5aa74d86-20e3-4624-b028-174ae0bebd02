# 🤗 How to Get Your FREE Hugging Face API Key

Follow these simple steps to get your free Hugging Face API key and unlock the full dynamic AI capabilities of your Choufli Hal chatbot!

## Step 1: Create a Hugging Face Account (FREE)

1. Go to [huggingface.co](https://huggingface.co)
2. Click "Sign Up" in the top right corner
3. Fill in your details:
   - Username
   - Email
   - Password
4. Verify your email address
5. You're in! No credit card required! 🎉

## Step 2: Generate Your API Token

1. Once logged in, click on your profile picture in the top right
2. Select "Settings" from the dropdown menu
3. In the left sidebar, click on "Access Tokens"
4. Click the "New token" button
5. Fill in the token details:
   - **Name**: `<PERSON><PERSON>li Hal Chatbot` (or any name you prefer)
   - **Role**: Select "Read" (this is sufficient for our chatbot)
6. Click "Generate a token"
7. **IMPORTANT**: Copy the token immediately! You won't be able to see it again.

## Step 3: Add the Token to Your Project

1. Open your project folder
2. Find the `.env.local` file
3. Replace `your_huggingface_api_key_here` with your actual token:

```bash
# Before
HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# After (example)
HUGGINGFACE_API_KEY=hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

4. Save the file
5. Restart your development server:
   ```bash
   npm run dev
   ```

## Step 4: Test Your Setup

1. Go to [http://localhost:3000/chat](http://localhost:3000/chat)
2. Look for the status indicator at the top of the chat
3. You should see: **🤖 Dynamic AI - Powered by Hugging Face (FREE!)**
4. If you see **⚡ Fallback Mode**, check your API key setup

## Step 5: Start Chatting!

Try these test messages to see the AI in action:

- `مرحبا! كيف الحال؟` (Hello! How are you?)
- `Tell me about Tunisian family traditions`
- `What would Choubir say about hard work?`
- `أحتاج نصيحة عن العائلة` (I need advice about family)

## 🎯 What You Get with the FREE API

- **Generous limits**: Thousands of requests per month
- **No credit card**: Completely free to use
- **Multiple models**: Access to various AI models
- **Fast responses**: Good performance for real-time chat
- **Reliable service**: Stable API with good uptime

## 🔧 Troubleshooting

### "Fallback Mode" Still Showing?

1. **Check your token**: Make sure it's copied correctly with no extra spaces
2. **Restart the server**: Stop (`Ctrl+C`) and restart (`npm run dev`)
3. **Check the file**: Ensure `.env.local` is in the root folder
4. **Token permissions**: Make sure you selected "Read" access when creating the token

### API Errors?

1. **Rate limits**: If you hit limits, wait a few minutes and try again
2. **Model availability**: Some models might be temporarily unavailable
3. **Network issues**: Check your internet connection

### Need Help?

- Check the [Hugging Face Documentation](https://huggingface.co/docs/api-inference/index)
- Visit the [Hugging Face Community](https://huggingface.co/spaces)
- Look at our project's README.md for more details

## 🌟 Pro Tips

1. **Keep your token secret**: Never share it publicly or commit it to version control
2. **Monitor usage**: Check your usage in Hugging Face settings
3. **Try different models**: You can experiment with different AI models in the code
4. **Backup your token**: Save it somewhere safe in case you need it again

---

**Enjoy your FREE dynamic AI chatbot! 🎭🤖**

The Choufli Hal family welcomes you to the world of intelligent conversations! 
أهلا وسهلا في عالم المحادثات الذكية!
