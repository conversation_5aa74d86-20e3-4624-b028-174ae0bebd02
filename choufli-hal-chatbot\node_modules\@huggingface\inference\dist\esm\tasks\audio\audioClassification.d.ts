import type { AudioClassificationInput, AudioClassificationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
import type { LegacyAudioInput } from "./utils.js";
export type AudioClassificationArgs = BaseArgs & (AudioClassificationInput | LegacyAudioInput);
/**
 * This task reads some audio input and outputs the likelihood of classes.
 * Recommended model:  superb/hubert-large-superb-er
 */
export declare function audioClassification(args: AudioClassificationArgs, options?: Options): Promise<AudioClassificationOutput>;
//# sourceMappingURL=audioClassification.d.ts.map