import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./model-libraries.js";
import type { PipelineType } from "./pipelines.js";
/**
 * Mapping from library name to its supported tasks.
 * HF-Inference API (serverless) should be disabled for all other (library, task) pairs beyond this mapping.
 * This mapping is partially generated automatically by "python-api-export-tasks" action in
 * huggingface/api-inference-community repo upon merge. For transformers, the mapping is manually
 * based on api-inference (hf_types.rs).
 */
export declare const LIBRARY_TASK_MAPPING: Partial<Record<ModelLibraryKey, PipelineType[]>>;
//# sourceMappingURL=library-to-tasks.d.ts.map