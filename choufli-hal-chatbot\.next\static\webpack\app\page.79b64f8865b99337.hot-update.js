"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ThemeToggle */ \"(app-pages-browser)/./src/app/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_ChoufliCharacters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ChoufliCharacters */ \"(app-pages-browser)/./src/app/components/ChoufliCharacters.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Navigation */ \"(app-pages-browser)/./src/app/components/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen choufli-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-4 py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl md:text-8xl font-bold text-glow mb-6\",\n                                style: {\n                                    color: 'var(--choufli-gold)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"emoji-bounce\",\n                                        children: \"\\uD83C\\uDFAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Choufli Hal \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"emoji-bounce\",\n                                        children: \"\\uD83C\\uDFAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 68\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-3xl font-medium text-white mb-4\",\n                                children: \"AI Chatbot inspired by Tunisia's beloved sitcom\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-amber-200 mb-8\",\n                                children: \"شوفلي حل - دردش مع الذكاء الاصطناعي المستوحى من المسلسل التونسي الشهير\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mt-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/chat\",\n                                        className: \"text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105\",\n                                        style: {\n                                            background: \"linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Start Chatting / ابدأ المحادثة\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDCAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/login\",\n                                        className: \"chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2\",\n                                        style: {\n                                            color: 'var(--foreground)',\n                                            borderColor: 'var(--choufli-accent)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Sign In / دخول\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDEAA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-container p-8 rounded-2xl text-center shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83E\\uDD16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    style: {\n                                        color: 'var(--choufli-primary)'\n                                    },\n                                    children: \"AI Powered Chat\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: \"Chat with an AI that understands Tunisian culture and the humor of Choufli Hal\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-container p-8 rounded-2xl text-center shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    style: {\n                                        color: 'var(--choufli-primary)'\n                                    },\n                                    children: \"Tunisian Spirit\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: \"Experience the warmth and humor of Tunisian family life through our chatbot\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-container p-8 rounded-2xl text-center shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    style: {\n                                        color: 'var(--choufli-primary)'\n                                    },\n                                    children: \"Family Friendly\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: \"Just like the show, our chat is family-friendly and full of positive vibes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChoufliCharacters__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chat-container p-12 rounded-2xl shadow-2xl text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-6\",\n                            style: {\n                                color: 'var(--choufli-primary)'\n                            },\n                            children: \"About Choufli Hal / عن شوفلي حل\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg leading-relaxed mb-6\",\n                            style: {\n                                color: 'var(--foreground)'\n                            },\n                            children: \"Choufli Hal is a beloved Tunisian sitcom that has brought laughter and joy to families across Tunisia and the Arab world. Our AI chatbot captures the spirit of this wonderful show, bringing you the warmth, humor, and family values that made it so special.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg leading-relaxed\",\n                            style: {\n                                color: 'var(--foreground)'\n                            },\n                            children: \"شوفلي حل مسلسل تونسي كوميدي محبوب جلب الضحك والفرح للعائلات في تونس والعالم العربي. يجسد روبوت الدردشة الخاص بنا روح هذا العرض الرائع، ويقدم لك الدفء والفكاهة والقيم العائلية التي جعلته مميزاً جداً.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 py-16 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-4xl font-bold mb-6 text-white\",\n                        children: \"Ready to Chat? / مستعد للدردشة؟\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-amber-200 mb-8\",\n                        children: \"Join thousands of users who are already enjoying conversations with our Choufli Hal AI\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/signup\",\n                                className: \"text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105\",\n                                style: {\n                                    background: \"linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Sign Up Free / تسجيل مجاني\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/chat\",\n                                className: \"chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2\",\n                                style: {\n                                    color: 'var(--foreground)',\n                                    borderColor: 'var(--choufli-accent)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Try Without Account / جرب بدون حساب\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"choufli-character\",\n                title: \"Choubir says: Ahlan wa sahlan! \\uD83D\\uDC4B\",\n                onClick: ()=>{\n                    const messages = [\n                        \"أهلا وسهلا! Welcome to Choufli Hal family!\",\n                        \"مرحبا بيك في عائلة شوفلي حل! Choubir welcomes you!\",\n                        \"يا أهلا وسهلا! Come join our family!\",\n                        \"الحمد لله، أهلا بيك معانا! Najet and I welcome you!\"\n                    ];\n                    const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n                    alert(randomMessage);\n                },\n                children: \"\\uD83D\\uDC68‍\\uD83D\\uDD27\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});