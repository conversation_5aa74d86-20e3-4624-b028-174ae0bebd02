import { type InferenceSnippet, type ModelDataMinimal } from "@huggingface/tasks";
import type { InferenceProviderMappingEntry, InferenceProviderOrPolicy } from "../types.js";
export type InferenceSnippetOptions = {
    streaming?: boolean;
    billTo?: string;
    accessToken?: string;
    directRequest?: boolean;
    endpointUrl?: string;
} & Record<string, unknown>;
export declare function getInferenceSnippets(model: ModelDataMinimal, provider: InferenceProviderOrPolicy, inferenceProviderMapping?: InferenceProviderMappingEntry, opts?: Record<string, unknown>): InferenceSnippet[];
//# sourceMappingURL=getInferenceSnippets.d.ts.map