'use client';

import { useState, useRef, useEffect } from 'react';

interface Message {
  id: number;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function Home() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: "<PERSON><PERSON> wa sahlan! ti hak houni ! Welcome to the Choufli 7al chatbot! I'm here to chat with you just like the friendly family from the show. What's on your mind today?",
      isUser: false,
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now(),
      text: inputMessage,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: inputMessage }),
      });

      const data = await response.json();

      if (response.ok) {
        const botMessage: Message = {
          id: Date.now() + 1,
          text: data.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, botMessage]);
      } else {
        throw new Error(data.error || 'Failed to get response');
      }
    } catch (error) {
      const errorMessage: Message = {
        id: Date.now() + 1,
        text: 'Sorry, I encountered an error. Please try again!',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen choufli-background">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 text-white p-6 shadow-2xl header-choufli">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold text-center font-serif text-glow">
            <span className="emoji-bounce">🎭</span> Choufli 7al Chatbot <span className="emoji-bounce">🎭</span>
          </h1>
          <p className="text-center mt-3 text-orange-100 text-lg font-medium">
            Chat with your friendly AI companion inspired by the beloved Tunisian sitcom!
          </p>
          <div className="text-center mt-2">
            <span className="text-yellow-200 text-sm">3aslema mar7be bik fi choufli7al chat manetsawarech de5el ll ta9ti3 w taryych ma3neha</span>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className="max-w-4xl mx-auto p-6 h-[calc(100vh-160px)] flex flex-col">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto mb-6 chat-container rounded-xl shadow-2xl p-6 scroll-smooth">
          <div className="space-y-6">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} message-container`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div
                  className={`max-w-[75%] p-4 rounded-2xl shadow-lg transform transition-all duration-300 hover:scale-105 ${
                    message.isUser
                      ? 'bg-gradient-to-br from-blue-500 via-blue-600 to-purple-600 text-white message-user'
                      : 'bg-gradient-to-br from-orange-400 via-red-400 to-yellow-400 text-white message-bot'
                  }`}
                >
                  <p className="text-sm font-medium leading-relaxed">{message.text}</p>
                  <p className="text-xs mt-2 opacity-80 flex items-center gap-1">
                    <span>{message.isUser ? '👤' : '🤖'}</span>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start message-container">
                <div className="bg-gradient-to-br from-orange-400 via-red-400 to-yellow-400 text-white p-4 rounded-2xl shadow-lg">
                  <div className="flex items-center gap-2">
                    <span className="text-sm">🤖 typing...</span>
                    <div className="loading-dots">
                      <div className="loading-dot"></div>
                      <div className="loading-dot"></div>
                      <div className="loading-dot"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="chat-container rounded-xl shadow-2xl p-6">
          <div className="flex space-x-4">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
              placeholder="echki ou bien otlob echt7eb 💬"
              className="flex-1 p-4 border-3 border-orange-300 rounded-xl focus:outline-none focus:border-orange-500 focus:ring-4 focus:ring-orange-200 font-medium text-lg input-choufli bg-white/95"
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={isLoading || !inputMessage.trim()}
              className="bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 text-white px-8 py-4 rounded-xl font-bold hover:from-orange-600 hover:via-red-600 hover:to-yellow-600 disabled:opacity-50 disabled:cursor-not-allowed btn-choufli shadow-xl text-lg"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="loading-dots">
                    <div className="loading-dot"></div>
                    <div className="loading-dot"></div>
                    <div className="loading-dot"></div>
                  </div>
                </div>
              ) : (
                <span className="flex items-center gap-2">
                  <span>send</span>
                  <span>📤</span>
                </span>
              )}
            </button>
          </div>
          <div className="mt-3 text-center">
            <p className="text-sm text-gray-600">
              <span className="emoji-bounce">✨</span>
              Press Enter to send • اضغط Enter للإرسال
              <span className="emoji-bounce">✨</span>
            </p>
          </div>
        </div>
      </div>

      {/* Choufli 7al Character Decoration */}
      <div
        className="choufli-character"
        title="Si Choubir says hello! 👋"
        onClick={() => {
          const messages = [
            "مرحبا! أهلا وسهلا!",
            "كيف الحال؟ شوفلي حل!",
            "الحمد لله، كلشي بخير!",
            "يا أهلا وسهلا بيك!"
          ];
          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          alert(randomMessage);
        }}
      >
        👨‍👩‍👧‍👦
      </div>
    </div>
  );
}
