'use client';

import Link from 'next/link';
import ThemeToggle from './components/ThemeToggle';
import LanguageToggle from './components/LanguageToggle';
import Navigation from './components/Navigation';
import { useLanguage } from './contexts/LanguageContext';

export default function Home() {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen choufli-background">
      <Navigation />
      <ThemeToggle />
      <LanguageToggle />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden pt-20">
        <div className="max-w-6xl mx-auto px-4 py-20">
          <div className="text-center">
            <h1 className="text-6xl md:text-8xl font-bold text-glow mb-6" style={{ color: 'var(--choufli-gold)' }}>
              <span className="emoji-bounce">🎭</span> {t('home.title')} <span className="emoji-bounce">🎭</span>
            </h1>
            <p className="text-2xl md:text-3xl font-medium text-white mb-4">
              {t('home.subtitle')}
            </p>
            <p className="text-xl text-amber-200 mb-8">
              {t('home.description')}
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mt-12">
              <Link
                href="/chat"
                className="text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105"
                style={{
                  background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
                }}
              >
                <span className="flex items-center gap-3">
                  <span>{t('home.startChat')}</span>
                  <span>💬</span>
                </span>
              </Link>

              <Link
                href="/login"
                className="chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2"
                style={{
                  color: 'var(--foreground)',
                  borderColor: 'var(--choufli-accent)'
                }}
              >
                <span className="flex items-center gap-3">
                  <span>{t('home.signIn')}</span>
                  <span>🚪</span>
                </span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="grid md:grid-cols-3 gap-8">
          <div className="chat-container p-8 rounded-2xl text-center shadow-xl">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-xl font-bold mb-4" style={{ color: 'var(--choufli-primary)' }}>
              {t('features.aiPowered')}
            </h3>
            <p style={{ color: 'var(--foreground)' }}>
              {t('features.aiDescription')}
            </p>
          </div>

          <div className="chat-container p-8 rounded-2xl text-center shadow-xl">
            <div className="text-4xl mb-4">🇹🇳</div>
            <h3 className="text-xl font-bold mb-4" style={{ color: 'var(--choufli-primary)' }}>
              {t('features.tunisianSpirit')}
            </h3>
            <p style={{ color: 'var(--foreground)' }}>
              {t('features.tunisianDescription')}
            </p>
          </div>

          <div className="chat-container p-8 rounded-2xl text-center shadow-xl">
            <div className="text-4xl mb-4">👨‍👩‍👧‍👦</div>
            <h3 className="text-xl font-bold mb-4" style={{ color: 'var(--choufli-primary)' }}>
              {t('features.familyFriendly')}
            </h3>
            <p style={{ color: 'var(--foreground)' }}>
              {t('features.familyDescription')}
            </p>
          </div>
        </div>
      </div>

      {/* About Section */}
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="chat-container p-12 rounded-2xl shadow-2xl text-center">
          <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--choufli-primary)' }}>
            {t('about.title')}
          </h2>
          <p className="text-lg leading-relaxed mb-6" style={{ color: 'var(--foreground)' }}>
            {t('about.description1')}
          </p>
          <p className="text-lg leading-relaxed" style={{ color: 'var(--foreground)' }}>
            {t('about.description2')}
          </p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="max-w-4xl mx-auto px-4 py-16 text-center">
        <h2 className="text-4xl font-bold mb-6 text-white">
          {t('home.readyToChat')}
        </h2>
        <p className="text-xl text-amber-200 mb-8">
          {t('home.joinUsers')}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/signup"
            className="text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105"
            style={{
              background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
            }}
          >
            <span className="flex items-center gap-3">
              <span>{t('home.signUpFree')}</span>
              <span>✨</span>
            </span>
          </Link>

          <Link
            href="/chat"
            className="chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2"
            style={{
              color: 'var(--foreground)',
              borderColor: 'var(--choufli-accent)'
            }}
          >
            <span className="flex items-center gap-3">
              <span>{t('home.tryWithoutAccount')}</span>
              <span>🚀</span>
            </span>
          </Link>
        </div>
      </div>

      {/* Floating Characters */}
      <div
        className="choufli-character"
        title="Choubir says: Ahlan wa sahlan! 👋"
        onClick={() => {
          const messages = [
            "أهلا وسهلا! Welcome to Choufli Hal family!",
            "مرحبا بيك في عائلة شوفلي حل! Choubir welcomes you!",
            "يا أهلا وسهلا! Come join our family!",
            "الحمد لله، أهلا بيك معانا! Najet and I welcome you!"
          ];
          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          alert(randomMessage);
        }}
      >
        👨‍🔧
      </div>
    </div>
  );
}
