'use client';

import Link from 'next/link';
import ThemeToggle from './components/ThemeToggle';
import ChoufliCharacters from './components/ChoufliCharacters';
import Navigation from './components/Navigation';

export default function Home() {
  return (
    <div className="min-h-screen choufli-background">
      <Navigation />
      <ThemeToggle />
      
      {/* Hero Section */}
      <div className="relative overflow-hidden pt-20">
        <div className="max-w-6xl mx-auto px-4 py-20">
          <div className="text-center">
            <h1 className="text-6xl md:text-8xl font-bold text-glow mb-6" style={{ color: 'var(--choufli-gold)' }}>
              <span className="emoji-bounce">🎭</span> Choufli Hal <span className="emoji-bounce">🎭</span>
            </h1>
            <p className="text-2xl md:text-3xl font-medium text-white mb-4">
              AI Chatbot inspired by Tunisia's beloved sitcom
            </p>
            <p className="text-xl text-amber-200 mb-8">
              شوفلي حل - دردش مع الذكاء الاصطناعي المستوحى من المسلسل التونسي الشهير
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mt-12">
              <Link 
                href="/chat"
                className="text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105"
                style={{
                  background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
                }}
              >
                <span className="flex items-center gap-3">
                  <span>Start Chatting / ابدأ المحادثة</span>
                  <span>💬</span>
                </span>
              </Link>
              
              <Link 
                href="/login"
                className="chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2"
                style={{
                  color: 'var(--foreground)',
                  borderColor: 'var(--choufli-accent)'
                }}
              >
                <span className="flex items-center gap-3">
                  <span>Sign In / دخول</span>
                  <span>🚪</span>
                </span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <div className="grid md:grid-cols-3 gap-8">
          <div className="chat-container p-8 rounded-2xl text-center shadow-xl">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-xl font-bold mb-4" style={{ color: 'var(--choufli-primary)' }}>
              AI Powered Chat
            </h3>
            <p style={{ color: 'var(--foreground)' }}>
              Chat with an AI that understands Tunisian culture and the humor of Choufli Hal
            </p>
          </div>
          
          <div className="chat-container p-8 rounded-2xl text-center shadow-xl">
            <div className="text-4xl mb-4">🇹🇳</div>
            <h3 className="text-xl font-bold mb-4" style={{ color: 'var(--choufli-primary)' }}>
              Tunisian Spirit
            </h3>
            <p style={{ color: 'var(--foreground)' }}>
              Experience the warmth and humor of Tunisian family life through our chatbot
            </p>
          </div>
          
          <div className="chat-container p-8 rounded-2xl text-center shadow-xl">
            <div className="text-4xl mb-4">👨‍👩‍👧‍👦</div>
            <h3 className="text-xl font-bold mb-4" style={{ color: 'var(--choufli-primary)' }}>
              Family Friendly
            </h3>
            <p style={{ color: 'var(--foreground)' }}>
              Just like the show, our chat is family-friendly and full of positive vibes
            </p>
          </div>
        </div>
      </div>

      {/* Characters Section */}
      <div className="max-w-6xl mx-auto px-4 py-16">
        <ChoufliCharacters />
      </div>

      {/* About Section */}
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="chat-container p-12 rounded-2xl shadow-2xl text-center">
          <h2 className="text-3xl font-bold mb-6" style={{ color: 'var(--choufli-primary)' }}>
            About Choufli Hal / عن شوفلي حل
          </h2>
          <p className="text-lg leading-relaxed mb-6" style={{ color: 'var(--foreground)' }}>
            Choufli Hal is a beloved Tunisian sitcom that has brought laughter and joy to families across Tunisia and the Arab world.
            Our AI chatbot captures the spirit of this wonderful show, bringing you the warmth, humor, and family values that made it so special.
          </p>
          <p className="text-lg leading-relaxed" style={{ color: 'var(--foreground)' }}>
            شوفلي حل مسلسل تونسي كوميدي محبوب جلب الضحك والفرح للعائلات في تونس والعالم العربي.
            يجسد روبوت الدردشة الخاص بنا روح هذا العرض الرائع، ويقدم لك الدفء والفكاهة والقيم العائلية التي جعلته مميزاً جداً.
          </p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="max-w-4xl mx-auto px-4 py-16 text-center">
        <h2 className="text-4xl font-bold mb-6 text-white">
          Ready to Chat? / مستعد للدردشة؟
        </h2>
        <p className="text-xl text-amber-200 mb-8">
          Join thousands of users who are already enjoying conversations with our Choufli Hal AI
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link 
            href="/signup"
            className="text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105"
            style={{
              background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
            }}
          >
            <span className="flex items-center gap-3">
              <span>Sign Up Free / تسجيل مجاني</span>
              <span>✨</span>
            </span>
          </Link>
          
          <Link 
            href="/chat"
            className="chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2"
            style={{
              color: 'var(--foreground)',
              borderColor: 'var(--choufli-accent)'
            }}
          >
            <span className="flex items-center gap-3">
              <span>Try Without Account / جرب بدون حساب</span>
              <span>🚀</span>
            </span>
          </Link>
        </div>
      </div>

      {/* Floating Characters */}
      <div
        className="choufli-character"
        title="Si Choubir says: Ahlan wa sahlan! 👋"
        onClick={() => {
          const messages = [
            "أهلا وسهلا! Welcome to Choufli Hal!",
            "مرحبا بيك في عائلة شوفلي حل!",
            "يا أهلا وسهلا! Come join the fun!",
            "الحمد لله، أهلا بيك معانا!"
          ];
          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          alert(randomMessage);
        }}
      >
        👨‍👩‍👧‍👦
      </div>
    </div>
  );
}
