{"$id": "/inference/schemas/translation/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Translation inference", "title": "TranslationInput", "type": "object", "properties": {"inputs": {"description": "The text to translate.", "type": "string"}, "parameters": {"description": "Additional inference parameters for Translation", "$ref": "#/$defs/TranslationParameters"}}, "$defs": {"TranslationParameters": {"title": "TranslationParameters", "type": "object", "properties": {"src_lang": {"type": "string", "description": "The source language of the text. Required for models that can translate from multiple languages."}, "tgt_lang": {"type": "string", "description": "Target language to translate to. Required for models that can translate to multiple languages."}, "clean_up_tokenization_spaces": {"type": "boolean", "description": "Whether to clean up the potential extra spaces in the text output."}, "truncation": {"title": "TranslationTruncationStrategy", "type": "string", "description": "The truncation strategy to use.", "enum": ["do_not_truncate", "longest_first", "only_first", "only_second"]}, "generate_parameters": {"title": "generateParameters", "type": "object", "description": "Additional parametrization of the text generation algorithm."}}}}, "required": ["inputs"]}