'use client';

import { useState, useEffect } from 'react';

export default function ChatStatus() {
  const [isOnline, setIsOnline] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // Check if the chat API is working
    const checkStatus = async () => {
      try {
        const response = await fetch('/api/chat', {
          method: 'GET',
        });
        
        if (response.ok) {
          setIsOnline(true);
        } else {
          setIsOnline(false);
        }
      } catch (error) {
        console.error('Failed to check chat status:', error);
        setIsOnline(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkStatus();
  }, []);

  if (isChecking) {
    return (
      <div className="flex items-center gap-2 text-sm" style={{ color: 'var(--foreground)' }}>
        <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
        <span>Checking AI status...</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 text-sm" style={{ color: 'var(--foreground)' }}>
      <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></div>
      <span>
        {isOnline ? (
          <>
            <span className="font-medium text-green-400">🤖 Dynamic AI</span> - Powered by Hugging Face (FREE!)
          </>
        ) : (
          <>
            <span className="font-medium text-yellow-400">⚡ Fallback Mode</span> - Get FREE Hugging Face API key
          </>
        )}
      </span>
    </div>
  );
}
