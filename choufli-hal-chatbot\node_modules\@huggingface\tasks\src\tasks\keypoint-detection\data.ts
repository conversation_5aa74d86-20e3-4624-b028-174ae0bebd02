import type { TaskDataCustom } from "../index.js";

const taskData: TaskDataCustom = {
	datasets: [
		{
			description: "A dataset of hand keypoints of over 500k examples.",
			id: "Vincent-luo/hagrid-mediapipe-hands",
		},
	],
	demo: {
		inputs: [
			{
				filename: "keypoint-detection-input.png",
				type: "img",
			},
		],
		outputs: [
			{
				filename: "keypoint-detection-output.png",
				type: "img",
			},
		],
	},
	metrics: [],
	models: [
		{
			description: "A robust keypoint detection model.",
			id: "magic-leap-community/superpoint",
		},
		{
			description: "A robust keypoint matching model.",
			id: "magic-leap-community/superglue_outdoor",
		},
		{
			description: "Strong keypoint detection model used to detect human pose.",
			id: "facebook/sapiens-pose-1b",
		},
		{
			description: "Powerful keypoint detection model used to detect human pose.",
			id: "usyd-community/vitpose-plus-base",
		},
	],
	spaces: [
		{
			description: "An application that detects hand keypoints in real-time.",
			id: "datasciencedojo/Hand-Keypoint-Detection-Realtime",
		},
		{
			description: "An application to try a universal keypoint detection model.",
			id: "merve/SuperPoint",
		},
	],
	summary: "Keypoint detection is the task of identifying meaningful distinctive points or features in an image.",
	widgetModels: [],
	youtubeId: "",
};

export default taskData;
