export declare enum GGMLFileQuantizationType {
    F32 = 0,
    F16 = 1,
    Q4_0 = 2,
    Q4_1 = 3,
    Q4_1_SOME_F16 = 4,
    Q4_2 = 5,
    Q4_3 = 6,
    Q8_0 = 7,
    Q5_0 = 8,
    Q5_1 = 9,
    Q2_K = 10,
    Q3_K_S = 11,
    Q3_K_M = 12,
    Q3_K_L = 13,
    Q4_K_S = 14,
    Q4_K_M = 15,
    Q5_K_S = 16,
    Q5_K_M = 17,
    Q6_K = 18,
    IQ2_XXS = 19,
    IQ2_XS = 20,
    Q2_K_S = 21,
    IQ3_XS = 22,
    IQ3_XXS = 23,
    IQ1_S = 24,
    IQ4_NL = 25,
    IQ3_S = 26,
    IQ3_M = 27,
    IQ2_S = 28,
    IQ2_M = 29,
    IQ4_XS = 30,
    IQ1_M = 31,
    BF16 = 32,
    Q4_0_4_4 = 33,
    Q4_0_4_8 = 34,
    Q4_0_8_8 = 35,
    TQ1_0 = 36,
    TQ2_0 = 37,
    Q2_K_XL = 1000,
    Q3_K_XL = 1001,
    Q4_K_XL = 1002,
    Q5_K_XL = 1003,
    Q6_K_XL = 1004,
    Q8_K_XL = 1005
}
export declare const GGUF_QUANT_RE: RegExp;
export declare const GGUF_QUANT_RE_GLOBAL: RegExp;
export declare function parseGGUFQuantLabel(fname: string): string | undefined;
export declare const GGUF_QUANT_ORDER: GGMLFileQuantizationType[];
export declare function findNearestQuantType(quant: GGMLFileQuantizationType, availableQuants: GGMLFileQuantizationType[]): GGMLFileQuantizationType | undefined;
export declare enum GGMLQuantizationType {
    F32 = 0,
    F16 = 1,
    Q4_0 = 2,
    Q4_1 = 3,
    Q5_0 = 6,
    Q5_1 = 7,
    Q8_0 = 8,
    Q8_1 = 9,
    Q2_K = 10,
    Q3_K = 11,
    Q4_K = 12,
    Q5_K = 13,
    Q6_K = 14,
    Q8_K = 15,
    IQ2_XXS = 16,
    IQ2_XS = 17,
    IQ3_XXS = 18,
    IQ1_S = 19,
    IQ4_NL = 20,
    IQ3_S = 21,
    IQ2_S = 22,
    IQ4_XS = 23,
    I8 = 24,
    I16 = 25,
    I32 = 26,
    I64 = 27,
    F64 = 28,
    IQ1_M = 29,
    BF16 = 30,
    TQ1_0 = 34,
    TQ2_0 = 35
}
//# sourceMappingURL=gguf.d.ts.map