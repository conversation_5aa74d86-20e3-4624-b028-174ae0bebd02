/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/LanguageContext.tsx */ \"(rsc)/./src/app/contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/ThemeContext.tsx */ \"(rsc)/./src/app/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGpvdWhhXFxcXE9uZURyaXZlXFxcXEJ1cmVhdVxcXFxzdGF0aWMgYm90IGNob3VmbGkgN2FsXFxcXGNob3VmbGktaGFsLWNoYXRib3RcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam91aGFcXE9uZURyaXZlXFxCdXJlYXVcXHN0YXRpYyBib3QgY2hvdWZsaSA3YWxcXGNob3VmbGktaGFsLWNoYXRib3RcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/contexts/LanguageContext.tsx":
/*!**********************************************!*\
  !*** ./src/app/contexts/LanguageContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\LanguageContext.tsx",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\LanguageContext.tsx",
"useLanguage",
);

/***/ }),

/***/ "(rsc)/./src/app/contexts/ThemeContext.tsx":
/*!*******************************************!*\
  !*** ./src/app/contexts/ThemeContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"984b8a3664e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdWhhXFxPbmVEcml2ZVxcQnVyZWF1XFxzdGF0aWMgYm90IGNob3VmbGkgN2FsXFxjaG91ZmxpLWhhbC1jaGF0Ym90XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5ODRiOGEzNjY0ZTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./contexts/ThemeContext */ \"(rsc)/./src/app/contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contexts/LanguageContext */ \"(rsc)/./src/app/contexts/LanguageContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Choufli 7al Chatbot - AI Chat Inspired by Tunisian Comedy\",\n    description: \"Chat with an AI assistant inspired by the beloved Tunisian sitcom Choufli 7al. Enjoy friendly conversations with a touch of Tunisian humor and warmth.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.LanguageProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/LanguageContext.tsx */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/ThemeContext.tsx */ \"(ssr)/./src/app/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3QlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1nZWlzdC1zYW5zJTVDJTIyJTJDJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyZ2Vpc3RTYW5zJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3RfTW9ubyU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWdlaXN0LW1vbm8lNUMlMjIlMkMlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJnZWlzdE1vbm8lNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDam91aGElNUMlNUNPbmVEcml2ZSU1QyU1Q0J1cmVhdSU1QyU1Q3N0YXRpYyUyMGJvdCUyMGNob3VmbGklMjA3YWwlNUMlNUNjaG91ZmxpLWhhbC1jaGF0Ym90JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDY29udGV4dHMlNUMlNUNMYW5ndWFnZUNvbnRleHQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTGFuZ3VhZ2VQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNqb3VoYSU1QyU1Q09uZURyaXZlJTVDJTVDQnVyZWF1JTVDJTVDc3RhdGljJTIwYm90JTIwY2hvdWZsaSUyMDdhbCU1QyU1Q2Nob3VmbGktaGFsLWNoYXRib3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjb250ZXh0cyU1QyU1Q1RoZW1lQ29udGV4dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUaGVtZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBbU07QUFDbk07QUFDQSxrTEFBNkwiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkxhbmd1YWdlUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxqb3VoYVxcXFxPbmVEcml2ZVxcXFxCdXJlYXVcXFxcc3RhdGljIGJvdCBjaG91ZmxpIDdhbFxcXFxjaG91ZmxpLWhhbC1jaGF0Ym90XFxcXHNyY1xcXFxhcHBcXFxcY29udGV4dHNcXFxcTGFuZ3VhZ2VDb250ZXh0LnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXGpvdWhhXFxcXE9uZURyaXZlXFxcXEJ1cmVhdVxcXFxzdGF0aWMgYm90IGNob3VmbGkgN2FsXFxcXGNob3VmbGktaGFsLWNoYXRib3RcXFxcc3JjXFxcXGFwcFxcXFxjb250ZXh0c1xcXFxUaGVtZUNvbnRleHQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBd0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXGpvdWhhXFxcXE9uZURyaXZlXFxcXEJ1cmVhdVxcXFxzdGF0aWMgYm90IGNob3VmbGkgN2FsXFxcXGNob3VmbGktaGFsLWNoYXRib3RcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/components/LanguageToggle.tsx":
/*!***********************************************!*\
  !*** ./src/app/components/LanguageToggle.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/LanguageContext */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { language, toggleLanguage } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleLanguage,\n        className: \"fixed top-6 left-6 z-50 p-3 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110\",\n        title: `Switch to ${language === 'en' ? 'Arabic' : 'English'}`,\n        style: {\n            background: `linear-gradient(135deg, var(--choufli-primary), var(--choufli-secondary))`\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 font-bold\",\n            children: language === 'en' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"AR\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/Navigation.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/Navigation.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/LanguageContext */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Navigation() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const navItems = [\n        {\n            href: '/',\n            label: t('nav.home'),\n            icon: '🏠'\n        },\n        {\n            href: '/chat',\n            label: t('nav.chat'),\n            icon: '💬'\n        },\n        {\n            href: '/login',\n            label: t('nav.login'),\n            icon: '🚪'\n        },\n        {\n            href: '/signup',\n            label: t('nav.signup'),\n            icon: '✨'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-40 chat-container border-b-2\",\n        style: {\n            borderColor: 'var(--border-color)',\n            backdropFilter: 'blur(10px)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 py-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 text-xl font-bold\",\n                        style: {\n                            color: 'var(--choufli-primary)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"\\uD83C\\uDFAD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t('home.title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center gap-6\",\n                        children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: `flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${pathname === item.href ? 'font-bold' : 'hover:opacity-80'}`,\n                                style: {\n                                    color: pathname === item.href ? 'var(--choufli-accent)' : 'var(--foreground)',\n                                    backgroundColor: pathname === item.href ? 'rgba(139, 69, 19, 0.1)' : 'transparent'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-2 rounded-lg\",\n                            style: {\n                                color: 'var(--foreground)'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/ThemeToggle.tsx":
/*!********************************************!*\
  !*** ./src/app/components/ThemeToggle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./src/app/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"fixed top-6 right-6 z-50 p-3 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 dark:from-gray-700 dark:to-gray-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110\",\n        title: `Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`,\n        children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 16,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 15,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 20,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 19,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/contexts/LanguageContext.tsx":
/*!**********************************************!*\
  !*** ./src/app/contexts/LanguageContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\nconst translations = {\n    en: {\n        // Navigation\n        'nav.home': 'Home',\n        'nav.chat': 'Chat',\n        'nav.login': 'Login',\n        'nav.signup': 'Sign Up',\n        // Home page\n        'home.title': 'Choufli Hal',\n        'home.subtitle': 'AI Chatbot inspired by Tunisia\\'s beloved sitcom',\n        'home.description': 'Chat with an AI inspired by the famous Tunisian series',\n        'home.startChat': 'Start Chatting',\n        'home.signIn': 'Sign In',\n        'home.signUpFree': 'Sign Up Free',\n        'home.tryWithoutAccount': 'Try Without Account',\n        'home.readyToChat': 'Ready to Chat?',\n        'home.joinUsers': 'Join thousands of users who are already enjoying conversations with our Choufli Hal AI',\n        // Features\n        'features.aiPowered': 'AI Powered Chat',\n        'features.aiDescription': 'Chat with an AI that understands Tunisian culture and the humor of Choufli Hal',\n        'features.tunisianSpirit': 'Tunisian Spirit',\n        'features.tunisianDescription': 'Experience the warmth and humor of Tunisian family life through our chatbot',\n        'features.familyFriendly': 'Family Friendly',\n        'features.familyDescription': 'Just like the show, our chat is family-friendly and full of positive vibes',\n        // About\n        'about.title': 'About Choufli Hal',\n        'about.description1': 'Choufli Hal is a beloved Tunisian sitcom that has brought laughter and joy to families across Tunisia and the Arab world.',\n        'about.description2': 'Our AI chatbot captures the spirit of this wonderful show, bringing you the warmth, humor, and family values that made it so special.',\n        // Chat\n        'chat.title': 'Choufli Hal Chat',\n        'chat.subtitle': 'Chat with your friendly AI companion!',\n        'chat.placeholder': 'Type your message here 💬',\n        'chat.send': 'Send',\n        'chat.typing': 'typing...',\n        'chat.pressEnter': 'Press Enter to send',\n        // Auth\n        'auth.email': 'Email',\n        'auth.password': 'Password',\n        'auth.confirmPassword': 'Confirm Password',\n        'auth.fullName': 'Full Name',\n        'auth.signIn': 'Sign In',\n        'auth.signUp': 'Sign Up',\n        'auth.welcomeBack': 'Welcome back',\n        'auth.joinFamily': 'Join our family!',\n        'auth.createAccount': 'Create your account to start chatting',\n        'auth.alreadyHaveAccount': 'Already have an account?',\n        'auth.dontHaveAccount': 'Don\\'t have an account?',\n        'auth.signInHere': 'Sign in here',\n        'auth.signUpHere': 'Sign up here',\n        'auth.backToHome': 'Back to Home',\n        // Common\n        'common.close': 'Close',\n        'common.loading': 'Loading...',\n        'common.welcome': 'Welcome!'\n    },\n    ar: {\n        // Navigation\n        'nav.home': 'الرئيسية',\n        'nav.chat': 'دردشة',\n        'nav.login': 'دخول',\n        'nav.signup': 'تسجيل',\n        // Home page\n        'home.title': 'شوفلي حل',\n        'home.subtitle': 'روبوت دردشة ذكي مستوحى من المسلسل التونسي الشهير',\n        'home.description': 'دردش مع الذكاء الاصطناعي المستوحى من المسلسل التونسي الشهير',\n        'home.startChat': 'ابدأ المحادثة',\n        'home.signIn': 'دخول',\n        'home.signUpFree': 'تسجيل مجاني',\n        'home.tryWithoutAccount': 'جرب بدون حساب',\n        'home.readyToChat': 'مستعد للدردشة؟',\n        'home.joinUsers': 'انضم لآلاف المستخدمين الذين يستمتعون بالمحادثات مع روبوت شوفلي حل',\n        // Features\n        'features.aiPowered': 'دردشة بالذكاء الاصطناعي',\n        'features.aiDescription': 'دردش مع ذكاء اصطناعي يفهم الثقافة التونسية وروح شوفلي حل',\n        'features.tunisianSpirit': 'الروح التونسية',\n        'features.tunisianDescription': 'اختبر دفء وفكاهة الحياة العائلية التونسية من خلال روبوت الدردشة',\n        'features.familyFriendly': 'مناسب للعائلة',\n        'features.familyDescription': 'مثل المسلسل تماماً، دردشتنا مناسبة للعائلة ومليئة بالطاقة الإيجابية',\n        // About\n        'about.title': 'عن شوفلي حل',\n        'about.description1': 'شوفلي حل مسلسل تونسي كوميدي محبوب جلب الضحك والفرح للعائلات في تونس والعالم العربي.',\n        'about.description2': 'يجسد روبوت الدردشة الخاص بنا روح هذا العرض الرائع، ويقدم لك الدفء والفكاهة والقيم العائلية التي جعلته مميزاً جداً.',\n        // Chat\n        'chat.title': 'دردشة شوفلي حل',\n        'chat.subtitle': 'دردش مع رفيقك الذكي الودود!',\n        'chat.placeholder': 'اكتب رسالتك هنا 💬',\n        'chat.send': 'إرسال',\n        'chat.typing': 'يكتب...',\n        'chat.pressEnter': 'اضغط Enter للإرسال',\n        // Auth\n        'auth.email': 'البريد الإلكتروني',\n        'auth.password': 'كلمة المرور',\n        'auth.confirmPassword': 'تأكيد كلمة المرور',\n        'auth.fullName': 'الاسم الكامل',\n        'auth.signIn': 'دخول',\n        'auth.signUp': 'تسجيل',\n        'auth.welcomeBack': 'مرحباً بعودتك',\n        'auth.joinFamily': 'انضم لعائلتنا!',\n        'auth.createAccount': 'أنشئ حسابك لبدء المحادثة',\n        'auth.alreadyHaveAccount': 'لديك حساب بالفعل؟',\n        'auth.dontHaveAccount': 'ليس لديك حساب؟',\n        'auth.signInHere': 'ادخل هنا',\n        'auth.signUpHere': 'سجل هنا',\n        'auth.backToHome': 'العودة للرئيسية',\n        // Common\n        'common.close': 'إغلاق',\n        'common.loading': 'جاري التحميل...',\n        'common.welcome': 'أهلاً وسهلاً!'\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Check if there's a saved language preference\n            const savedLanguage = localStorage.getItem('choufli-language');\n            if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            if (mounted) {\n                localStorage.setItem('choufli-language', language);\n                // Apply RTL for Arabic\n                document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n                document.documentElement.lang = language;\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language,\n        mounted\n    ]);\n    const toggleLanguage = ()=>{\n        setLanguage((prev)=>prev === 'en' ? 'ar' : 'en');\n    };\n    const t = (key)=>{\n        return translations[language][key] || key;\n    };\n    if (!mounted) {\n        return null; // Prevent hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            toggleLanguage,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/contexts/ThemeContext.tsx":
/*!*******************************************!*\
  !*** ./src/app/contexts/ThemeContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dark'); // Default to dark theme\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Check if there's a saved theme preference\n            const savedTheme = localStorage.getItem('choufli-theme');\n            if (savedTheme) {\n                setTheme(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (mounted) {\n                localStorage.setItem('choufli-theme', theme);\n                // Apply theme to document\n                document.documentElement.setAttribute('data-theme', theme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'dark' ? 'light' : 'dark');\n    };\n    if (!mounted) {\n        return null; // Prevent hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ThemeToggle */ \"(ssr)/./src/app/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/LanguageToggle */ \"(ssr)/./src/app/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Navigation */ \"(ssr)/./src/app/components/Navigation.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./contexts/LanguageContext */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen choufli-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative overflow-hidden pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto px-4 py-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl md:text-8xl font-bold text-glow mb-6\",\n                                style: {\n                                    color: 'var(--choufli-gold)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"emoji-bounce\",\n                                        children: \"\\uD83C\\uDFAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" \",\n                                    t('home.title'),\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"emoji-bounce\",\n                                        children: \"\\uD83C\\uDFAD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 74\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-2xl md:text-3xl font-medium text-white mb-4\",\n                                children: t('home.subtitle')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-amber-200 mb-8\",\n                                children: t('home.description')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mt-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/chat\",\n                                        className: \"text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105\",\n                                        style: {\n                                            background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t('home.startChat')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 41,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDCAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/login\",\n                                        className: \"chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2\",\n                                        style: {\n                                            color: 'var(--foreground)',\n                                            borderColor: 'var(--choufli-accent)'\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t('home.signIn')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDEAA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto px-4 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-container p-8 rounded-2xl text-center shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83E\\uDD16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    style: {\n                                        color: 'var(--choufli-primary)'\n                                    },\n                                    children: t('features.aiPowered')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: t('features.aiDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-container p-8 rounded-2xl text-center shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    style: {\n                                        color: 'var(--choufli-primary)'\n                                    },\n                                    children: t('features.tunisianSpirit')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: t('features.tunisianDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"chat-container p-8 rounded-2xl text-center shadow-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl mb-4\",\n                                    children: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold mb-4\",\n                                    style: {\n                                        color: 'var(--choufli-primary)'\n                                    },\n                                    children: t('features.familyFriendly')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: t('features.familyDescription')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"chat-container p-12 rounded-2xl shadow-2xl text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-6\",\n                            style: {\n                                color: 'var(--choufli-primary)'\n                            },\n                            children: t('about.title')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg leading-relaxed mb-6\",\n                            style: {\n                                color: 'var(--foreground)'\n                            },\n                            children: t('about.description1')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg leading-relaxed\",\n                            style: {\n                                color: 'var(--foreground)'\n                            },\n                            children: t('about.description2')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto px-4 py-16 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-4xl font-bold mb-6 text-white\",\n                        children: t('home.readyToChat')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-amber-200 mb-8\",\n                        children: t('home.joinUsers')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/signup\",\n                                className: \"text-white px-8 py-4 rounded-xl font-bold text-xl btn-choufli shadow-xl transition-all duration-300 hover:scale-105\",\n                                style: {\n                                    background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t('home.signUpFree')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/chat\",\n                                className: \"chat-container px-8 py-4 rounded-xl font-bold text-xl transition-all duration-300 hover:scale-105 border-2\",\n                                style: {\n                                    color: 'var(--foreground)',\n                                    borderColor: 'var(--choufli-accent)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: t('home.tryWithoutAccount')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\uD83D\\uDE80\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"choufli-character\",\n                title: \"Choubir says: Ahlan wa sahlan! \\uD83D\\uDC4B\",\n                onClick: ()=>{\n                    const messages = [\n                        \"أهلا وسهلا! Welcome to Choufli Hal family!\",\n                        \"مرحبا بيك في عائلة شوفلي حل! Choubir welcomes you!\",\n                        \"يا أهلا وسهلا! Come join our family!\",\n                        \"الحمد لله، أهلا بيك معانا! Najet and I welcome you!\"\n                    ];\n                    const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n                    alert(randomMessage);\n                },\n                children: \"\\uD83D\\uDC68‍\\uD83D\\uDD27\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();