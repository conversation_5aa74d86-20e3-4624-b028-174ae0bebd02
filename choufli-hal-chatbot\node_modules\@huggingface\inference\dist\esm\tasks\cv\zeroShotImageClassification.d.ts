import type { ZeroShotImageClassificationInput, ZeroShotImageClassificationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
/**
 * @deprecated
 */
interface LegacyZeroShotImageClassificationInput {
    inputs: {
        image: Blob | <PERSON><PERSON><PERSON>Buffer;
    };
}
export type ZeroShotImageClassificationArgs = BaseArgs & (ZeroShotImageClassificationInput | LegacyZeroShotImageClassificationInput);
/**
 * Classify an image to specified classes.
 * Recommended model: openai/clip-vit-large-patch14-336
 */
export declare function zeroShotImageClassification(args: ZeroShotImageClassificationArgs, options?: Options): Promise<ZeroShotImageClassificationOutput>;
export {};
//# sourceMappingURL=zeroShotImageClassification.d.ts.map