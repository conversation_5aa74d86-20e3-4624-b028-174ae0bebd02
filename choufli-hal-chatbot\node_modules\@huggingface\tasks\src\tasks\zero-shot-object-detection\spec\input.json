{"$id": "/inference/schemas/zero-shot-object-detection/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Zero Shot Object Detection inference", "title": "ZeroShotObjectDetectionInput", "type": "object", "properties": {"inputs": {"description": "The input image data as a base64-encoded string.", "type": "string", "comment": "type=binary"}, "parameters": {"description": "Additional inference parameters for Zero Shot Object Detection", "$ref": "#/$defs/ZeroShotObjectDetectionParameters"}}, "$defs": {"ZeroShotObjectDetectionParameters": {"title": "ZeroShotObjectDetectionParameters", "type": "object", "properties": {"candidate_labels": {"description": "The candidate labels for this image", "type": "array", "items": {"type": "string"}}}, "required": ["candidate_labels"]}}, "required": ["inputs", "parameters"]}