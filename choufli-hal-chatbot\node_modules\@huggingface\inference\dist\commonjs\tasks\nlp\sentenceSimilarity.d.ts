import type { SentenceSimilarityInput, SentenceSimilarityOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type SentenceSimilarityArgs = BaseArgs & SentenceSimilarityInput;
/**
 * Calculate the semantic similarity between one text and a list of other sentences by comparing their embeddings.
 */
export declare function sentenceSimilarity(args: SentenceSimilarityArgs, options?: Options): Promise<SentenceSimilarityOutput>;
//# sourceMappingURL=sentenceSimilarity.d.ts.map