@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Tajawal:wght@300;400;500;700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
  --choufli-orange: #ff6b35;
  --choufli-red: #e63946;
  --choufli-yellow: #ffd23f;
  --choufli-green: #06d6a0;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Tajawal', Arial, Helvetica, sans-serif;
  overflow-x: hidden;
}

/* Choufli 7al Background */
.choufli-background {
  background: linear-gradient(135deg, #ff6b35 0%, #ffd23f 25%, #e63946 50%, #06d6a0 75%, #ff6b35 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  position: relative;
}

.choufli-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /*background-image: url('https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');*/
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.08;
  z-index: 0;
}

/* Choufli 7al character decoration */
.choufli-character {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #ff6b35, #ffd23f);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  animation: bounce 3s infinite;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.choufli-character:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(255, 107, 53, 0.5);
}

.choufli-background > * {
  position: relative;
  z-index: 1;
}

/* Animations */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-30px,0); }
  70% { transform: translate3d(0,-15px,0); }
  90% { transform: translate3d(0,-4px,0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 40px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(100px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

/* Message animations */
.message-user {
  animation: slideInRight 0.5s ease-out;
}

.message-bot {
  animation: slideInLeft 0.5s ease-out;
}

.message-container {
  animation: fadeInUp 0.6s ease-out;
}

/* Button hover effects */
.btn-choufli {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-choufli::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-choufli:hover::before {
  left: 100%;
}

.btn-choufli:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Input field effects */
.input-choufli {
  background-color: #ff6b35;
  transition: all 0.3s ease;
  position: relative;
}

.input-choufli:focus {
  transform: scale(1.02);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

/* Chat container effects */
.chat-container {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(255, 107, 53, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Header animation */
.header-choufli {
  animation: fadeInUp 1s ease-out;
}

/* Emoji bounce */
.emoji-bounce {
  animation: bounce 2s infinite;
  display: inline-block;
}

/* Loading dots */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: loadingDot 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scroll animation */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
}

/* Responsive font sizes */
@media (max-width: 768px) {
  .header-choufli h1 {
    font-size: 1.5rem;
  }
}
