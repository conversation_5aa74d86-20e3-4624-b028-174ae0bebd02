@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Tajawal:wght@300;400;500;700&display=swap');

:root {
  /* Light theme colors */
  --background-light: #f8f9fa;
  --foreground-light: #2d3748;
  --card-light: rgba(255, 255, 255, 0.95);
  --border-light: rgba(139, 69, 19, 0.3);

  /* Dark theme colors (default) */
  --background-dark: #1a1a2e;
  --foreground-dark: #e2e8f0;
  --card-dark: rgba(30, 30, 60, 0.95);
  --border-dark: rgba(139, 69, 19, 0.5);

  /* Choufli Hal themed colors - darker palette */
  --choufli-primary: #8b4513;    /* Saddle brown */
  --choufli-secondary: #a0522d;  /* Sienna */
  --choufli-accent: #cd853f;     /* Peru */
  --choufli-gold: #daa520;       /* Goldenrod */
  --choufli-deep: #654321;       /* Dark brown */
  --choufli-warm: #d2691e;       /* Chocolate */

  /* Default to dark theme */
  --background: var(--background-dark);
  --foreground: var(--foreground-dark);
  --card-bg: var(--card-dark);
  --border-color: var(--border-dark);
}

/* Light theme */
[data-theme="light"] {
  --background: var(--background-light);
  --foreground: var(--foreground-light);
  --card-bg: var(--card-light);
  --border-color: var(--border-light);
}

/* Dark theme */
[data-theme="dark"] {
  --background: var(--background-dark);
  --foreground: var(--foreground-dark);
  --card-bg: var(--card-dark);
  --border-color: var(--border-dark);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Tajawal', Arial, Helvetica, sans-serif;
  overflow-x: hidden;
}

/* Choufli 7al Background */
.choufli-background {
  background: linear-gradient(135deg, var(--choufli-primary) 0%, var(--choufli-secondary) 25%, var(--choufli-deep) 50%, var(--choufli-warm) 75%, var(--choufli-accent) 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  position: relative;
  min-height: 100vh;
  color: var(--foreground);
}

.choufli-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /*background-image: url('https://images.unsplash.com/photo-1606041008023-472dfb5e530f?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80');*/
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.08;
  z-index: 0;
}

/* Choufli 7al character decoration */
.choufli-character {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--choufli-primary), var(--choufli-gold));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  animation: bounce 3s infinite;
  box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
}

.choufli-character:hover {
  transform: scale(1.1);
  box-shadow: 0 12px 35px rgba(139, 69, 19, 0.6);
}

.choufli-background > * {
  position: relative;
  z-index: 1;
}

/* Animations */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-30px,0); }
  70% { transform: translate3d(0,-15px,0); }
  90% { transform: translate3d(0,-4px,0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 40px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translate3d(100px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

/* Message animations */
.message-user {
  animation: slideInRight 0.5s ease-out;
}

.message-bot {
  animation: slideInLeft 0.5s ease-out;
}

.message-container {
  animation: fadeInUp 0.6s ease-out;
}

/* Button hover effects */
.btn-choufli {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-choufli::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-choufli:hover::before {
  left: 100%;
}

.btn-choufli:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Input field effects */
.input-choufli {
  background-color: var(--card-bg);
  color: var(--foreground);
  border-color: var(--border-color);
  transition: all 0.3s ease;
  position: relative;
}

.input-choufli:focus {
  transform: scale(1.02);
  box-shadow: 0 0 20px rgba(139, 69, 19, 0.4);
  border-color: var(--choufli-accent);
}

/* Chat container effects */
.chat-container {
  backdrop-filter: blur(10px);
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Header animation */
.header-choufli {
  animation: fadeInUp 1s ease-out;
}

/* Emoji bounce */
.emoji-bounce {
  animation: bounce 2s infinite;
  display: inline-block;
}

/* Loading dots */
.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: loadingDot 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDot {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Scroll animation */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Text glow effect */
.text-glow {
  text-shadow: 0 0 10px rgba(139, 69, 19, 0.6);
}

/* Additional theme-aware styles */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Enhanced button styles */
.btn-choufli:active {
  transform: translateY(1px) scale(0.98);
}

/* Character card hover effects */
.character-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.character-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(139, 69, 19, 0.2);
}

/* Improved focus states for accessibility */
.input-choufli:focus,
.btn-choufli:focus {
  outline: 2px solid var(--choufli-accent);
  outline-offset: 2px;
}

/* Enhanced gradient animations */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Responsive font sizes */
@media (max-width: 768px) {
  .header-choufli h1 {
    font-size: 1.5rem;
  }

  .text-6xl {
    font-size: 2.5rem;
  }

  .text-8xl {
    font-size: 3rem;
  }

  .choufli-character {
    width: 80px;
    height: 80px;
    font-size: 2rem;
    bottom: 15px;
    right: 15px;
  }
}
