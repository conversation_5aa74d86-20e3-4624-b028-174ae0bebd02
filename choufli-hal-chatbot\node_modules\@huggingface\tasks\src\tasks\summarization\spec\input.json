{"$id": "/inference/schemas/summarization/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Summarization inference", "title": "SummarizationInput", "type": "object", "properties": {"inputs": {"description": "The input text to summarize.", "type": "string"}, "parameters": {"description": "Additional inference parameters for summarization.", "$ref": "#/$defs/SummarizationParameters"}}, "$defs": {"SummarizationParameters": {"title": "SummarizationParameters", "type": "object", "properties": {"clean_up_tokenization_spaces": {"type": "boolean", "description": "Whether to clean up the potential extra spaces in the text output."}, "truncation": {"title": "SummarizationTruncationStrategy", "type": "string", "description": "The truncation strategy to use.", "enum": ["do_not_truncate", "longest_first", "only_first", "only_second"]}, "generate_parameters": {"title": "generateParameters", "type": "object", "description": "Additional parametrization of the text generation algorithm."}}}}, "required": ["inputs"]}