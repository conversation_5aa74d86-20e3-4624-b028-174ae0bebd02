/**
 * This file contains the (simplified) types used
 * to represent queries that are made to Elastic
 * in order to count number of model downloads
 *
 * Read this doc about download stats on the Hub:
 *
 * https://huggingface.co/docs/hub/models-download-stats
 * Available fields:
 *  - path: the complete file path (relative) (e.g: "prefix/file.extension")
 *  - path_prefix: the prefix of the file path (e.g: "prefix/", empty if no prefix)
 *  - path_extension: the extension of the file path (e.g: "extension")
 *  - path_filename: the name of the file path (e.g: "file")
 * see also:
 * https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-query-string-query.html
 */

export type ElasticSearchQuery = string;
