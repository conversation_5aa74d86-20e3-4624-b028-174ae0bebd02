{"$id": "/inference/schemas/sentence-similarity/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Sentence similarity inference", "title": "SentenceSimilarityInput", "type": "object", "properties": {"inputs": {"title": "SentenceSimilarityInputData", "type": "object", "properties": {"source_sentence": {"description": "The string that you wish to compare the other strings with. This can be a phrase, sentence, or longer passage, depending on the model being used.", "type": "string"}, "sentences": {"type": "array", "description": "A list of strings which will be compared against the source_sentence.", "items": {"type": "string"}}}, "required": ["source_sentence", "sentences"]}, "parameters": {"description": "Additional inference parameters for Sentence Similarity", "$ref": "#/$defs/SentenceSimilarityParameters"}}, "$defs": {"SentenceSimilarityParameters": {"title": "SentenceSimilarityParameters", "type": "object", "properties": {}}}, "required": ["inputs"]}