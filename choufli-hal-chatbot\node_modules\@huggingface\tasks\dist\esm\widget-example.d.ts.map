{"version": 3, "file": "widget-example.d.ts", "sourceRoot": "", "sources": ["../../src/widget-example.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,EAAE,0BAA0B,EAAE,MAAM,kBAAkB,CAAC;AAEnE,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AAGrD,MAAM,MAAM,yBAAyB,GAAG,KAAK,CAAC;IAAE,KAAK,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,MAAM,CAAA;CAAE,CAAC,CAAC;AAChF,MAAM,WAAW,8BAA8B;IAC9C,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;CACd;AACD,MAAM,WAAW,uBAAuB;IACvC,IAAI,EAAE,MAAM,CAAC;CACb;AACD,MAAM,WAAW,sBAAsB;IACtC,GAAG,EAAE,MAAM,CAAC;CACZ;AAED,MAAM,MAAM,mBAAmB,GAC5B,yBAAyB,GACzB,8BAA8B,GAC9B,uBAAuB,GACvB,sBAAsB,CAAC;AAG1B,MAAM,WAAW,iBAAiB,CAAC,OAAO;IACzC,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,KAAK,CAAC,EAAE,MAAM,CAAC;IACf;;;OAGG;IACH,UAAU,CAAC,EAAE;QAEZ,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAE9B,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,KAAK,CAAC,EAAE,MAAM,CAAC;QACf,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,SAAS,CAAC,EAAE,OAAO,CAAC;QAEpB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,mBAAmB,CAAC,EAAE,MAAM,CAAC;KAC7B,CAAC;IACF;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CACjB;AAED,MAAM,WAAW,sBAAsB,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,iBAAiB,CAAC,OAAO,CAAC;IACxG,QAAQ,EAAE,0BAA0B,EAAE,CAAC;CACvC;AAED,MAAM,WAAW,sBAAsB,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,iBAAiB,CAAC,OAAO,CAAC;IACxG,IAAI,EAAE,MAAM,CAAC;CACb;AAED,MAAM,WAAW,gCAAgC,CAAC,OAAO,GAAG,mBAAmB,CAC9E,SAAQ,sBAAsB,CAAC,OAAO,CAAC;IACvC,OAAO,EAAE,MAAM,CAAC;CAChB;AAED,MAAM,WAAW,8BAA8B,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,sBAAsB,CAAC,OAAO,CAAC;IACrH,KAAK,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,uBAAuB,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,iBAAiB,CAAC,OAAO,CAAC;IACzG,GAAG,EAAE,MAAM,CAAC;CACZ;AACD,MAAM,WAAW,gCAAgC,CAAC,OAAO,GAAG,mBAAmB,CAC9E,SAAQ,uBAAuB,CAAC,OAAO,CAAC;IACxC,MAAM,EAAE,MAAM,CAAC;CACf;AAED,MAAM,MAAM,8BAA8B,CAAC,OAAO,GAAG,mBAAmB,IAAI,uBAAuB,CAAC,OAAO,CAAC,GAC3G,sBAAsB,CAAC,OAAO,CAAC,CAAC;AAEjC,MAAM,MAAM,kCAAkC,CAAC,OAAO,GAAG,mBAAmB,IAAI,uBAAuB,CAAC,OAAO,CAAC,GAC/G,8BAA8B,CAAC,OAAO,CAAC,CAAC;AAEzC,MAAM,WAAW,gCAAgC,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,iBAAiB,CAAC,OAAO,CAAC;IAClH,eAAe,EAAE,SAAS,CAAC;CAC3B;AAED,MAAM,WAAW,2BAA2B,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,iBAAiB,CAAC,OAAO,CAAC;IAC7G,KAAK,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,8BAA8B,CAAC,OAAO,GAAG,mBAAmB,CAAE,SAAQ,sBAAsB,CAAC,OAAO,CAAC;IACrH,IAAI,EAAE,MAAM,CAAC;IACb,gBAAgB,EAAE,MAAM,CAAC;IACzB,WAAW,EAAE,OAAO,CAAC;CACrB;AAED,MAAM,WAAW,oCAAoC,CAAC,OAAO,GAAG,mBAAmB,CAClF,SAAQ,iBAAiB,CAAC,OAAO,CAAC;IAClC,eAAe,EAAE,MAAM,CAAC;IACxB,SAAS,EAAE,MAAM,EAAE,CAAC;CACpB;AAID,MAAM,MAAM,aAAa,CAAC,OAAO,GAAG,mBAAmB,IACpD,sBAAsB,CAAC,OAAO,CAAC,GAC/B,sBAAsB,CAAC,OAAO,CAAC,GAC/B,gCAAgC,CAAC,OAAO,CAAC,GACzC,8BAA8B,CAAC,OAAO,CAAC,GACvC,uBAAuB,CAAC,OAAO,CAAC,GAChC,gCAAgC,CAAC,OAAO,CAAC,GACzC,8BAA8B,CAAC,OAAO,CAAC,GACvC,kCAAkC,CAAC,OAAO,CAAC,GAC3C,gCAAgC,CAAC,OAAO,CAAC,GACzC,2BAA2B,CAAC,OAAO,CAAC,GACpC,8BAA8B,CAAC,OAAO,CAAC,GACvC,oCAAoC,CAAC,OAAO,CAAC,CAAC;AAEjD,KAAK,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;AAE1D,MAAM,MAAM,sBAAsB,GAAG,WAAW,CAAC,aAAa,CAAC,CAAC"}