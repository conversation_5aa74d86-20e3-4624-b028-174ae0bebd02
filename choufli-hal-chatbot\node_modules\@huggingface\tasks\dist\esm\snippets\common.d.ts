import type { ChatCompletionInputMessage, GenerationParameters } from "../tasks/index.js";
export declare function stringifyMessages(messages: ChatCompletionInputMessage[], opts?: {
    indent?: string;
    attributeKeyQuotes?: boolean;
    customContentEscaper?: (str: string) => string;
}): string;
type PartialGenerationParameters = Partial<Pick<GenerationParameters, "temperature" | "max_tokens" | "top_p">>;
export declare function stringifyGenerationConfig(config: PartialGenerationParameters, opts: {
    indent: string;
    attributeValueConnector: string;
    attributeKeyQuotes?: boolean;
}): string;
export {};
//# sourceMappingURL=common.d.ts.map