import type { TaskDataCustom } from "../index.js";

const taskData: TaskDataCustom = {
	datasets: [
		{
			description: "A large dataset of over 10 million 3D objects.",
			id: "allenai/objaverse-xl",
		},
		{
			description: "Descriptive captions for 3D objects in Objaverse.",
			id: "tiange/Cap3D",
		},
	],
	demo: {
		inputs: [
			{
				label: "Prompt",
				content: "a cat statue",
				type: "text",
			},
		],
		outputs: [
			{
				label: "Result",
				content: "text-to-3d-3d-output-filename.glb",
				type: "text",
			},
		],
	},
	metrics: [],
	models: [
		{
			description: "Text-to-3D mesh model by OpenAI",
			id: "openai/shap-e",
		},
		{
			description: "Generative 3D gaussian splatting model.",
			id: "ashawkey/LGM",
		},
	],
	spaces: [
		{
			description: "Text-to-3D demo with mesh outputs.",
			id: "hysts/Shap-E",
		},
		{
			description: "Text/image-to-3D demo with splat outputs.",
			id: "ashawkey/LGM",
		},
	],
	summary: "Text-to-3D models take in text input and produce 3D output.",
	widgetModels: [],
	youtubeId: "",
};

export default taskData;
