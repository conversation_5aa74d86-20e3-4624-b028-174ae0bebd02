'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import ThemeToggle from '../components/ThemeToggle';
import LanguageToggle from '../components/LanguageToggle';
import Navigation from '../components/Navigation';
import { useLanguage } from '../contexts/LanguageContext';

export default function LoginPage() {
  const { t } = useLanguage();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate login process
    setTimeout(() => {
      setIsLoading(false);
      // For demo purposes, redirect to chat
      router.push('/');
    }, 1500);
  };

  return (
    <div className="min-h-screen choufli-background flex items-center justify-center p-4 pt-20">
      <Navigation />
      <ThemeToggle />
      <LanguageToggle />
      
      <div className="chat-container w-full max-w-md p-8 rounded-2xl shadow-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-glow mb-2" style={{ color: 'var(--choufli-primary)' }}>
            <span className="emoji-bounce">🎭</span> {t('home.title')}
          </h1>
          <p className="text-lg font-medium" style={{ color: 'var(--foreground)' }}>
            {t('auth.welcomeBack')}
          </p>
          <p className="text-sm mt-2 opacity-75" style={{ color: 'var(--foreground)' }}>
            {t('auth.createAccount')}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-2" style={{ color: 'var(--foreground)' }}>
              {t('auth.email')}
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="input-choufli w-full p-4 rounded-xl border-2 focus:outline-none focus:ring-4 focus:ring-opacity-50"
              placeholder="<EMAIL>"
              required
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium mb-2" style={{ color: 'var(--foreground)' }}>
              {t('auth.password')}
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input-choufli w-full p-4 rounded-xl border-2 focus:outline-none focus:ring-4 focus:ring-opacity-50"
              placeholder="••••••••"
              required
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            className="w-full text-white py-4 px-6 rounded-xl font-bold text-lg btn-choufli shadow-xl transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            style={{
              background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
            }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="loading-dots">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
                <span>Signing in...</span>
              </div>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <span>{t('auth.signIn')}</span>
                <span>🚪</span>
              </span>
            )}
          </button>
        </form>

        <div className="mt-8 text-center">
          <p className="text-sm" style={{ color: 'var(--foreground)' }}>
            {t('auth.dontHaveAccount')}
          </p>
          <Link
            href="/signup"
            className="text-lg font-medium hover:underline transition-colors duration-300"
            style={{ color: 'var(--choufli-accent)' }}
          >
            {t('auth.signUpHere')}
          </Link>
        </div>

        <div className="mt-6 text-center">
          <Link
            href="/"
            className="text-sm hover:underline transition-colors duration-300"
            style={{ color: 'var(--choufli-secondary)' }}
          >
            ← {t('auth.backToHome')}
          </Link>
        </div>
      </div>

      {/* Decorative character */}
      <div
        className="choufli-character"
        title="Si Choubir welcomes you! 👋"
        onClick={() => {
          const messages = [
            "أهلا وسهلا! Welcome back!",
            "مرحبا بعودتك! Good to see you again!",
            "الحمد لله، أهلا بيك!",
            "يا أهلا وسهلا! Come on in!"
          ];
          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          alert(randomMessage);
        }}
      >
        👨‍💼
      </div>
    </div>
  );
}
