/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/page";
exports.ids = ["app/chat/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/page\",\n        pathname: \"/chat\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/LanguageContext.tsx */ \"(rsc)/./src/app/contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/ThemeContext.tsx */ \"(rsc)/./src/app/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2NoYXQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxqb3VoYVxcXFxPbmVEcml2ZVxcXFxCdXJlYXVcXFxcc3RhdGljIGJvdCBjaG91ZmxpIDdhbFxcXFxjaG91ZmxpLWhhbC1jaGF0Ym90XFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam91aGFcXE9uZURyaXZlXFxCdXJlYXVcXHN0YXRpYyBib3QgY2hvdWZsaSA3YWxcXGNob3VmbGktaGFsLWNoYXRib3RcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\chat\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/contexts/LanguageContext.tsx":
/*!**********************************************!*\
  !*** ./src/app/contexts/LanguageContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),
/* harmony export */   useLanguage: () => (/* binding */ useLanguage)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const LanguageProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\LanguageContext.tsx",
"LanguageProvider",
);const useLanguage = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\LanguageContext.tsx",
"useLanguage",
);

/***/ }),

/***/ "(rsc)/./src/app/contexts/ThemeContext.tsx":
/*!*******************************************!*\
  !*** ./src/app/contexts/ThemeContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\ThemeContext.tsx",
"ThemeProvider",
);const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Bureau\\static bot choufli 7al\\choufli-hal-chatbot\\src\\app\\contexts\\ThemeContext.tsx",
"useTheme",
);

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"984b8a3664e4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdWhhXFxPbmVEcml2ZVxcQnVyZWF1XFxzdGF0aWMgYm90IGNob3VmbGkgN2FsXFxjaG91ZmxpLWhhbC1jaGF0Ym90XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI5ODRiOGEzNjY0ZTRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./contexts/ThemeContext */ \"(rsc)/./src/app/contexts/ThemeContext.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./contexts/LanguageContext */ \"(rsc)/./src/app/contexts/LanguageContext.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Choufli 7al Chatbot - AI Chat Inspired by Tunisian Comedy\",\n    description: \"Chat with an AI assistant inspired by the beloved Tunisian sitcom Choufli 7al. Enjoy friendly conversations with a touch of Tunisian humor and warmth.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.LanguageProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/LanguageContext.tsx */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contexts/ThemeContext.tsx */ \"(ssr)/./src/app/contexts/ThemeContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CLanguageContext.tsx%22%2C%22ids%22%3A%5B%22LanguageProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Ccontexts%5C%5CThemeContext.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(ssr)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2pvdWhhJTVDJTVDT25lRHJpdmUlNUMlNUNCdXJlYXUlNUMlNUNzdGF0aWMlMjBib3QlMjBjaG91ZmxpJTIwN2FsJTVDJTVDY2hvdWZsaS1oYWwtY2hhdGJvdCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2NoYXQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEpBQThJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxqb3VoYVxcXFxPbmVEcml2ZVxcXFxCdXJlYXVcXFxcc3RhdGljIGJvdCBjaG91ZmxpIDdhbFxcXFxjaG91ZmxpLWhhbC1jaGF0Ym90XFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cjouha%5C%5COneDrive%5C%5CBureau%5C%5Cstatic%20bot%20choufli%207al%5C%5Cchoufli-hal-chatbot%5C%5Csrc%5C%5Capp%5C%5Cchat%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/ThemeToggle */ \"(ssr)/./src/app/components/ThemeToggle.tsx\");\n/* harmony import */ var _components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/LanguageToggle */ \"(ssr)/./src/app/components/LanguageToggle.tsx\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/Navigation */ \"(ssr)/./src/app/components/Navigation.tsx\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../contexts/LanguageContext */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ChatPage() {\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            text: \"Ahlan wa sahlan! ti hak houni ! Welcome to the Choufli 7al chatbot! I'm here to chat with you just like the friendly family from the show. What's on your mind today?\",\n            isUser: false,\n            timestamp: new Date()\n        }\n    ]);\n    const [inputMessage, setInputMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPage.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatPage.useEffect\"], [\n        messages\n    ]);\n    const sendMessage = async ()=>{\n        if (!inputMessage.trim() || isLoading) return;\n        const userMessage = {\n            id: Date.now(),\n            text: inputMessage,\n            isUser: true,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setInputMessage('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: inputMessage\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                const botMessage = {\n                    id: Date.now() + 1,\n                    text: data.response,\n                    isUser: false,\n                    timestamp: new Date()\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        botMessage\n                    ]);\n            } else {\n                throw new Error(data.error || 'Failed to get response');\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now() + 1,\n                text: 'Sorry, I encountered an error. Please try again!',\n                isUser: false,\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen choufli-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ThemeToggle__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LanguageToggle__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white p-6 shadow-2xl header-choufli mt-16\",\n                style: {\n                    background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                    href: \"/\",\n                                    className: \"text-white hover:text-amber-200 transition-colors duration-300\",\n                                    title: \"Back to Home\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M10 19l-7-7m0 0l7-7m-7 7h18\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-4xl font-bold text-glow\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"emoji-bounce\",\n                                                    children: \"\\uD83C\\uDFAD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                t('chat.title'),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"emoji-bounce\",\n                                                    children: \"\\uD83C\\uDFAD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 76\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-amber-100 text-lg font-medium\",\n                                            children: t('chat.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                \" \"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-amber-200 text-sm\",\n                                children: \"3aslema mar7be bik fi choufli7al chat manetsawarech de5el ll ta9ti3 w taryych ma3neha\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto p-6 h-[calc(100vh-160px)] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto mb-6 chat-container rounded-xl shadow-2xl p-6 scroll-smooth\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `flex ${message.isUser ? 'justify-end' : 'justify-start'} message-container`,\n                                        style: {\n                                            animationDelay: `${index * 0.1}s`\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `max-w-[75%] p-4 rounded-2xl shadow-lg transform transition-all duration-300 hover:scale-105 ${message.isUser ? 'bg-gradient-to-br from-slate-600 via-slate-700 to-slate-800 text-white message-user' : 'text-white message-bot'}`,\n                                            style: !message.isUser ? {\n                                                background: `linear-gradient(to bottom right, var(--choufli-secondary), var(--choufli-warm), var(--choufli-accent))`\n                                            } : {},\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium leading-relaxed\",\n                                                    children: message.text\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs mt-2 opacity-80 flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: message.isUser ? '👤' : '🤖'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        message.timestamp.toLocaleTimeString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, message.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)),\n                                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-start message-container\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-white p-4 rounded-2xl shadow-lg\",\n                                        style: {\n                                            background: `linear-gradient(to bottom right, var(--choufli-secondary), var(--choufli-warm), var(--choufli-accent))`\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: \"\\uD83E\\uDD16 typing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"loading-dots\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"loading-dot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"loading-dot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"loading-dot\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-container rounded-xl shadow-2xl p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: inputMessage,\n                                        onChange: (e)=>setInputMessage(e.target.value),\n                                        onKeyDown: (e)=>{\n                                            if (e.key === 'Enter' && !e.shiftKey) {\n                                                e.preventDefault();\n                                                sendMessage();\n                                            }\n                                        },\n                                        placeholder: t('chat.placeholder'),\n                                        className: \"flex-1 p-4 border-3 rounded-xl focus:outline-none focus:ring-4 focus:ring-opacity-50 font-medium text-lg input-choufli\",\n                                        disabled: isLoading,\n                                        style: {\n                                            borderColor: 'var(--border-color)',\n                                            backgroundColor: 'var(--card-bg)',\n                                            color: 'var(--foreground)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: sendMessage,\n                                        disabled: isLoading || !inputMessage.trim(),\n                                        className: \"text-white px-8 py-4 rounded-xl font-bold disabled:opacity-50 disabled:cursor-not-allowed btn-choufli shadow-xl text-lg transition-all duration-300 hover:scale-105\",\n                                        style: {\n                                            background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`\n                                        },\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-dots\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-dot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-dot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"loading-dot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: t('chat.send')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDCE4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    style: {\n                                        color: 'var(--foreground)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"emoji-bounce\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this),\n                                        t('chat.pressEnter'),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"emoji-bounce\",\n                                            children: \"✨\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"choufli-character\",\n                title: \"Choubir says hello! \\uD83D\\uDC4B\",\n                onClick: ()=>{\n                    const messages = [\n                        \"مرحبا! أهلا وسهلا! Choubir here!\",\n                        \"كيف الحال؟ شوفلي حل! How are you?\",\n                        \"الحمد لله، كلشي بخير! Everything is good!\",\n                        \"يا أهلا وسهلا بيك! Welcome to our chat!\"\n                    ];\n                    const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n                    alert(randomMessage);\n                },\n                children: \"\\uD83D\\uDC68‍\\uD83D\\uDD27\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/chat/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/LanguageToggle.tsx":
/*!***********************************************!*\
  !*** ./src/app/components/LanguageToggle.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LanguageToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/LanguageContext */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction LanguageToggle() {\n    const { language, toggleLanguage } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_1__.useLanguage)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleLanguage,\n        className: \"fixed top-6 left-6 z-50 p-3 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110\",\n        title: `Switch to ${language === 'en' ? 'Arabic' : 'English'}`,\n        style: {\n            background: `linear-gradient(135deg, var(--choufli-primary), var(--choufli-secondary))`\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-2 font-bold\",\n            children: language === 'en' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\uD83C\\uDDF9\\uD83C\\uDDF3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"AR\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"EN\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\LanguageToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/LanguageToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/Navigation.tsx":
/*!*******************************************!*\
  !*** ./src/app/components/Navigation.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/LanguageContext */ \"(ssr)/./src/app/contexts/LanguageContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Navigation() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { t } = (0,_contexts_LanguageContext__WEBPACK_IMPORTED_MODULE_3__.useLanguage)();\n    const navItems = [\n        {\n            href: '/',\n            label: t('nav.home'),\n            icon: '🏠'\n        },\n        {\n            href: '/chat',\n            label: t('nav.chat'),\n            icon: '💬'\n        },\n        {\n            href: '/login',\n            label: t('nav.login'),\n            icon: '🚪'\n        },\n        {\n            href: '/signup',\n            label: t('nav.signup'),\n            icon: '✨'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-40 chat-container border-b-2\",\n        style: {\n            borderColor: 'var(--border-color)',\n            backdropFilter: 'blur(10px)'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-4 py-3\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 text-xl font-bold\",\n                        style: {\n                            color: 'var(--choufli-primary)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"\\uD83C\\uDFAD\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t('home.title')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center gap-6\",\n                        children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: `flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${pathname === item.href ? 'font-bold' : 'hover:opacity-80'}`,\n                                style: {\n                                    color: pathname === item.href ? 'var(--choufli-accent)' : 'var(--foreground)',\n                                    backgroundColor: pathname === item.href ? 'rgba(139, 69, 19, 0.1)' : 'transparent'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.href, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"p-2 rounded-lg\",\n                            style: {\n                                color: 'var(--foreground)'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\Navigation.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/components/ThemeToggle.tsx":
/*!********************************************!*\
  !*** ./src/app/components/ThemeToggle.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./src/app/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggle() {\n    const { theme, toggleTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: toggleTheme,\n        className: \"fixed top-6 right-6 z-50 p-3 rounded-full bg-gradient-to-r from-purple-600 to-blue-600 dark:from-gray-700 dark:to-gray-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110\",\n        title: `Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`,\n        children: theme === 'dark' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 16,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 15,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n                lineNumber: 20,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n            lineNumber: 19,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ThemeToggle.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/contexts/LanguageContext.tsx":
/*!**********************************************!*\
  !*** ./src/app/contexts/LanguageContext.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \n\nconst translations = {\n    en: {\n        // Navigation\n        'nav.home': 'Home',\n        'nav.chat': 'Chat',\n        'nav.login': 'Login',\n        'nav.signup': 'Sign Up',\n        // Home page\n        'home.title': 'Choufli Hal',\n        'home.subtitle': 'AI Chatbot inspired by Tunisia\\'s beloved sitcom',\n        'home.description': 'Chat with an AI inspired by the famous Tunisian series',\n        'home.startChat': 'Start Chatting',\n        'home.signIn': 'Sign In',\n        'home.signUpFree': 'Sign Up Free',\n        'home.tryWithoutAccount': 'Try Without Account',\n        'home.readyToChat': 'Ready to Chat?',\n        'home.joinUsers': 'Join thousands of users who are already enjoying conversations with our Choufli Hal AI',\n        // Features\n        'features.aiPowered': 'AI Powered Chat',\n        'features.aiDescription': 'Chat with an AI that understands Tunisian culture and the humor of Choufli Hal',\n        'features.tunisianSpirit': 'Tunisian Spirit',\n        'features.tunisianDescription': 'Experience the warmth and humor of Tunisian family life through our chatbot',\n        'features.familyFriendly': 'Family Friendly',\n        'features.familyDescription': 'Just like the show, our chat is family-friendly and full of positive vibes',\n        // About\n        'about.title': 'About Choufli Hal',\n        'about.description1': 'Choufli Hal is a beloved Tunisian sitcom that has brought laughter and joy to families across Tunisia and the Arab world.',\n        'about.description2': 'Our AI chatbot captures the spirit of this wonderful show, bringing you the warmth, humor, and family values that made it so special.',\n        // Chat\n        'chat.title': 'Choufli Hal Chat',\n        'chat.subtitle': 'Chat with your friendly AI companion!',\n        'chat.placeholder': 'Type your message here 💬',\n        'chat.send': 'Send',\n        'chat.typing': 'typing...',\n        'chat.pressEnter': 'Press Enter to send',\n        // Auth\n        'auth.email': 'Email',\n        'auth.password': 'Password',\n        'auth.confirmPassword': 'Confirm Password',\n        'auth.fullName': 'Full Name',\n        'auth.signIn': 'Sign In',\n        'auth.signUp': 'Sign Up',\n        'auth.welcomeBack': 'Welcome back',\n        'auth.joinFamily': 'Join our family!',\n        'auth.createAccount': 'Create your account to start chatting',\n        'auth.alreadyHaveAccount': 'Already have an account?',\n        'auth.dontHaveAccount': 'Don\\'t have an account?',\n        'auth.signInHere': 'Sign in here',\n        'auth.signUpHere': 'Sign up here',\n        'auth.backToHome': 'Back to Home',\n        // Common\n        'common.close': 'Close',\n        'common.loading': 'Loading...',\n        'common.welcome': 'Welcome!'\n    },\n    ar: {\n        // Navigation\n        'nav.home': 'الرئيسية',\n        'nav.chat': 'دردشة',\n        'nav.login': 'دخول',\n        'nav.signup': 'تسجيل',\n        // Home page\n        'home.title': 'شوفلي حل',\n        'home.subtitle': 'روبوت دردشة ذكي مستوحى من المسلسل التونسي الشهير',\n        'home.description': 'دردش مع الذكاء الاصطناعي المستوحى من المسلسل التونسي الشهير',\n        'home.startChat': 'ابدأ المحادثة',\n        'home.signIn': 'دخول',\n        'home.signUpFree': 'تسجيل مجاني',\n        'home.tryWithoutAccount': 'جرب بدون حساب',\n        'home.readyToChat': 'مستعد للدردشة؟',\n        'home.joinUsers': 'انضم لآلاف المستخدمين الذين يستمتعون بالمحادثات مع روبوت شوفلي حل',\n        // Features\n        'features.aiPowered': 'دردشة بالذكاء الاصطناعي',\n        'features.aiDescription': 'دردش مع ذكاء اصطناعي يفهم الثقافة التونسية وروح شوفلي حل',\n        'features.tunisianSpirit': 'الروح التونسية',\n        'features.tunisianDescription': 'اختبر دفء وفكاهة الحياة العائلية التونسية من خلال روبوت الدردشة',\n        'features.familyFriendly': 'مناسب للعائلة',\n        'features.familyDescription': 'مثل المسلسل تماماً، دردشتنا مناسبة للعائلة ومليئة بالطاقة الإيجابية',\n        // About\n        'about.title': 'عن شوفلي حل',\n        'about.description1': 'شوفلي حل مسلسل تونسي كوميدي محبوب جلب الضحك والفرح للعائلات في تونس والعالم العربي.',\n        'about.description2': 'يجسد روبوت الدردشة الخاص بنا روح هذا العرض الرائع، ويقدم لك الدفء والفكاهة والقيم العائلية التي جعلته مميزاً جداً.',\n        // Chat\n        'chat.title': 'دردشة شوفلي حل',\n        'chat.subtitle': 'دردش مع رفيقك الذكي الودود!',\n        'chat.placeholder': 'اكتب رسالتك هنا 💬',\n        'chat.send': 'إرسال',\n        'chat.typing': 'يكتب...',\n        'chat.pressEnter': 'اضغط Enter للإرسال',\n        // Auth\n        'auth.email': 'البريد الإلكتروني',\n        'auth.password': 'كلمة المرور',\n        'auth.confirmPassword': 'تأكيد كلمة المرور',\n        'auth.fullName': 'الاسم الكامل',\n        'auth.signIn': 'دخول',\n        'auth.signUp': 'تسجيل',\n        'auth.welcomeBack': 'مرحباً بعودتك',\n        'auth.joinFamily': 'انضم لعائلتنا!',\n        'auth.createAccount': 'أنشئ حسابك لبدء المحادثة',\n        'auth.alreadyHaveAccount': 'لديك حساب بالفعل؟',\n        'auth.dontHaveAccount': 'ليس لديك حساب؟',\n        'auth.signInHere': 'ادخل هنا',\n        'auth.signUpHere': 'سجل هنا',\n        'auth.backToHome': 'العودة للرئيسية',\n        // Common\n        'common.close': 'إغلاق',\n        'common.loading': 'جاري التحميل...',\n        'common.welcome': 'أهلاً وسهلاً!'\n    }\n};\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LanguageProvider({ children }) {\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Check if there's a saved language preference\n            const savedLanguage = localStorage.getItem('choufli-language');\n            if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {\n                setLanguage(savedLanguage);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            if (mounted) {\n                localStorage.setItem('choufli-language', language);\n                // Apply RTL for Arabic\n                document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';\n                document.documentElement.lang = language;\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        language,\n        mounted\n    ]);\n    const toggleLanguage = ()=>{\n        setLanguage((prev)=>prev === 'en' ? 'ar' : 'en');\n    };\n    const t = (key)=>{\n        return translations[language][key] || key;\n    };\n    if (!mounted) {\n        return null; // Prevent hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            toggleLanguage,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\nfunction useLanguage() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        throw new Error('useLanguage must be used within a LanguageProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2NvbnRleHRzL0xhbmd1YWdlQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU4RTtBQVU5RSxNQUFNSyxlQUFlO0lBQ25CQyxJQUFJO1FBQ0YsYUFBYTtRQUNiLFlBQVk7UUFDWixZQUFZO1FBQ1osYUFBYTtRQUNiLGNBQWM7UUFFZCxZQUFZO1FBQ1osY0FBYztRQUNkLGlCQUFpQjtRQUNqQixvQkFBb0I7UUFDcEIsa0JBQWtCO1FBQ2xCLGVBQWU7UUFDZixtQkFBbUI7UUFDbkIsMEJBQTBCO1FBQzFCLG9CQUFvQjtRQUNwQixrQkFBa0I7UUFFbEIsV0FBVztRQUNYLHNCQUFzQjtRQUN0QiwwQkFBMEI7UUFDMUIsMkJBQTJCO1FBQzNCLGdDQUFnQztRQUNoQywyQkFBMkI7UUFDM0IsOEJBQThCO1FBRTlCLFFBQVE7UUFDUixlQUFlO1FBQ2Ysc0JBQXNCO1FBQ3RCLHNCQUFzQjtRQUV0QixPQUFPO1FBQ1AsY0FBYztRQUNkLGlCQUFpQjtRQUNqQixvQkFBb0I7UUFDcEIsYUFBYTtRQUNiLGVBQWU7UUFDZixtQkFBbUI7UUFFbkIsT0FBTztRQUNQLGNBQWM7UUFDZCxpQkFBaUI7UUFDakIsd0JBQXdCO1FBQ3hCLGlCQUFpQjtRQUNqQixlQUFlO1FBQ2YsZUFBZTtRQUNmLG9CQUFvQjtRQUNwQixtQkFBbUI7UUFDbkIsc0JBQXNCO1FBQ3RCLDJCQUEyQjtRQUMzQix3QkFBd0I7UUFDeEIsbUJBQW1CO1FBQ25CLG1CQUFtQjtRQUNuQixtQkFBbUI7UUFFbkIsU0FBUztRQUNULGdCQUFnQjtRQUNoQixrQkFBa0I7UUFDbEIsa0JBQWtCO0lBQ3BCO0lBQ0FDLElBQUk7UUFDRixhQUFhO1FBQ2IsWUFBWTtRQUNaLFlBQVk7UUFDWixhQUFhO1FBQ2IsY0FBYztRQUVkLFlBQVk7UUFDWixjQUFjO1FBQ2QsaUJBQWlCO1FBQ2pCLG9CQUFvQjtRQUNwQixrQkFBa0I7UUFDbEIsZUFBZTtRQUNmLG1CQUFtQjtRQUNuQiwwQkFBMEI7UUFDMUIsb0JBQW9CO1FBQ3BCLGtCQUFrQjtRQUVsQixXQUFXO1FBQ1gsc0JBQXNCO1FBQ3RCLDBCQUEwQjtRQUMxQiwyQkFBMkI7UUFDM0IsZ0NBQWdDO1FBQ2hDLDJCQUEyQjtRQUMzQiw4QkFBOEI7UUFFOUIsUUFBUTtRQUNSLGVBQWU7UUFDZixzQkFBc0I7UUFDdEIsc0JBQXNCO1FBRXRCLE9BQU87UUFDUCxjQUFjO1FBQ2QsaUJBQWlCO1FBQ2pCLG9CQUFvQjtRQUNwQixhQUFhO1FBQ2IsZUFBZTtRQUNmLG1CQUFtQjtRQUVuQixPQUFPO1FBQ1AsY0FBYztRQUNkLGlCQUFpQjtRQUNqQix3QkFBd0I7UUFDeEIsaUJBQWlCO1FBQ2pCLGVBQWU7UUFDZixlQUFlO1FBQ2Ysb0JBQW9CO1FBQ3BCLG1CQUFtQjtRQUNuQixzQkFBc0I7UUFDdEIsMkJBQTJCO1FBQzNCLHdCQUF3QjtRQUN4QixtQkFBbUI7UUFDbkIsbUJBQW1CO1FBQ25CLG1CQUFtQjtRQUVuQixTQUFTO1FBQ1QsZ0JBQWdCO1FBQ2hCLGtCQUFrQjtRQUNsQixrQkFBa0I7SUFDcEI7QUFDRjtBQUVBLE1BQU1DLGdDQUFrQlAsb0RBQWFBLENBQWtDUTtBQUVoRSxTQUFTQyxpQkFBaUIsRUFBRUMsUUFBUSxFQUFpQztJQUMxRSxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR1YsK0NBQVFBLENBQVc7SUFDbkQsTUFBTSxDQUFDVyxTQUFTQyxXQUFXLEdBQUdaLCtDQUFRQSxDQUFDO0lBRXZDQyxnREFBU0E7c0NBQUM7WUFDUlcsV0FBVztZQUNYLCtDQUErQztZQUMvQyxNQUFNQyxnQkFBZ0JDLGFBQWFDLE9BQU8sQ0FBQztZQUMzQyxJQUFJRixpQkFBa0JBLENBQUFBLGtCQUFrQixRQUFRQSxrQkFBa0IsSUFBRyxHQUFJO2dCQUN2RUgsWUFBWUc7WUFDZDtRQUNGO3FDQUFHLEVBQUU7SUFFTFosZ0RBQVNBO3NDQUFDO1lBQ1IsSUFBSVUsU0FBUztnQkFDWEcsYUFBYUUsT0FBTyxDQUFDLG9CQUFvQlA7Z0JBQ3pDLHVCQUF1QjtnQkFDdkJRLFNBQVNDLGVBQWUsQ0FBQ0MsR0FBRyxHQUFHVixhQUFhLE9BQU8sUUFBUTtnQkFDM0RRLFNBQVNDLGVBQWUsQ0FBQ0UsSUFBSSxHQUFHWDtZQUNsQztRQUNGO3FDQUFHO1FBQUNBO1FBQVVFO0tBQVE7SUFFdEIsTUFBTVUsaUJBQWlCO1FBQ3JCWCxZQUFZWSxDQUFBQSxPQUFRQSxTQUFTLE9BQU8sT0FBTztJQUM3QztJQUVBLE1BQU1DLElBQUksQ0FBQ0M7UUFDVCxPQUFPdEIsWUFBWSxDQUFDTyxTQUFTLENBQUNlLElBQXVDLElBQUlBO0lBQzNFO0lBRUEsSUFBSSxDQUFDYixTQUFTO1FBQ1osT0FBTyxNQUFNLDZCQUE2QjtJQUM1QztJQUVBLHFCQUNFLDhEQUFDTixnQkFBZ0JvQixRQUFRO1FBQUNDLE9BQU87WUFBRWpCO1lBQVVZO1lBQWdCRTtRQUFFO2tCQUM1RGY7Ozs7OztBQUdQO0FBRU8sU0FBU21CO0lBQ2QsTUFBTUMsVUFBVTdCLGlEQUFVQSxDQUFDTTtJQUMzQixJQUFJdUIsWUFBWXRCLFdBQVc7UUFDekIsTUFBTSxJQUFJdUIsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam91aGFcXE9uZURyaXZlXFxCdXJlYXVcXHN0YXRpYyBib3QgY2hvdWZsaSA3YWxcXGNob3VmbGktaGFsLWNoYXRib3RcXHNyY1xcYXBwXFxjb250ZXh0c1xcTGFuZ3VhZ2VDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG50eXBlIExhbmd1YWdlID0gJ2VuJyB8ICdhcic7XG5cbmludGVyZmFjZSBMYW5ndWFnZUNvbnRleHRUeXBlIHtcbiAgbGFuZ3VhZ2U6IExhbmd1YWdlO1xuICB0b2dnbGVMYW5ndWFnZTogKCkgPT4gdm9pZDtcbiAgdDogKGtleTogc3RyaW5nKSA9PiBzdHJpbmc7XG59XG5cbmNvbnN0IHRyYW5zbGF0aW9ucyA9IHtcbiAgZW46IHtcbiAgICAvLyBOYXZpZ2F0aW9uXG4gICAgJ25hdi5ob21lJzogJ0hvbWUnLFxuICAgICduYXYuY2hhdCc6ICdDaGF0JyxcbiAgICAnbmF2LmxvZ2luJzogJ0xvZ2luJyxcbiAgICAnbmF2LnNpZ251cCc6ICdTaWduIFVwJyxcbiAgICBcbiAgICAvLyBIb21lIHBhZ2VcbiAgICAnaG9tZS50aXRsZSc6ICdDaG91ZmxpIEhhbCcsXG4gICAgJ2hvbWUuc3VidGl0bGUnOiAnQUkgQ2hhdGJvdCBpbnNwaXJlZCBieSBUdW5pc2lhXFwncyBiZWxvdmVkIHNpdGNvbScsXG4gICAgJ2hvbWUuZGVzY3JpcHRpb24nOiAnQ2hhdCB3aXRoIGFuIEFJIGluc3BpcmVkIGJ5IHRoZSBmYW1vdXMgVHVuaXNpYW4gc2VyaWVzJyxcbiAgICAnaG9tZS5zdGFydENoYXQnOiAnU3RhcnQgQ2hhdHRpbmcnLFxuICAgICdob21lLnNpZ25Jbic6ICdTaWduIEluJyxcbiAgICAnaG9tZS5zaWduVXBGcmVlJzogJ1NpZ24gVXAgRnJlZScsXG4gICAgJ2hvbWUudHJ5V2l0aG91dEFjY291bnQnOiAnVHJ5IFdpdGhvdXQgQWNjb3VudCcsXG4gICAgJ2hvbWUucmVhZHlUb0NoYXQnOiAnUmVhZHkgdG8gQ2hhdD8nLFxuICAgICdob21lLmpvaW5Vc2Vycyc6ICdKb2luIHRob3VzYW5kcyBvZiB1c2VycyB3aG8gYXJlIGFscmVhZHkgZW5qb3lpbmcgY29udmVyc2F0aW9ucyB3aXRoIG91ciBDaG91ZmxpIEhhbCBBSScsXG4gICAgXG4gICAgLy8gRmVhdHVyZXNcbiAgICAnZmVhdHVyZXMuYWlQb3dlcmVkJzogJ0FJIFBvd2VyZWQgQ2hhdCcsXG4gICAgJ2ZlYXR1cmVzLmFpRGVzY3JpcHRpb24nOiAnQ2hhdCB3aXRoIGFuIEFJIHRoYXQgdW5kZXJzdGFuZHMgVHVuaXNpYW4gY3VsdHVyZSBhbmQgdGhlIGh1bW9yIG9mIENob3VmbGkgSGFsJyxcbiAgICAnZmVhdHVyZXMudHVuaXNpYW5TcGlyaXQnOiAnVHVuaXNpYW4gU3Bpcml0JyxcbiAgICAnZmVhdHVyZXMudHVuaXNpYW5EZXNjcmlwdGlvbic6ICdFeHBlcmllbmNlIHRoZSB3YXJtdGggYW5kIGh1bW9yIG9mIFR1bmlzaWFuIGZhbWlseSBsaWZlIHRocm91Z2ggb3VyIGNoYXRib3QnLFxuICAgICdmZWF0dXJlcy5mYW1pbHlGcmllbmRseSc6ICdGYW1pbHkgRnJpZW5kbHknLFxuICAgICdmZWF0dXJlcy5mYW1pbHlEZXNjcmlwdGlvbic6ICdKdXN0IGxpa2UgdGhlIHNob3csIG91ciBjaGF0IGlzIGZhbWlseS1mcmllbmRseSBhbmQgZnVsbCBvZiBwb3NpdGl2ZSB2aWJlcycsXG4gICAgXG4gICAgLy8gQWJvdXRcbiAgICAnYWJvdXQudGl0bGUnOiAnQWJvdXQgQ2hvdWZsaSBIYWwnLFxuICAgICdhYm91dC5kZXNjcmlwdGlvbjEnOiAnQ2hvdWZsaSBIYWwgaXMgYSBiZWxvdmVkIFR1bmlzaWFuIHNpdGNvbSB0aGF0IGhhcyBicm91Z2h0IGxhdWdodGVyIGFuZCBqb3kgdG8gZmFtaWxpZXMgYWNyb3NzIFR1bmlzaWEgYW5kIHRoZSBBcmFiIHdvcmxkLicsXG4gICAgJ2Fib3V0LmRlc2NyaXB0aW9uMic6ICdPdXIgQUkgY2hhdGJvdCBjYXB0dXJlcyB0aGUgc3Bpcml0IG9mIHRoaXMgd29uZGVyZnVsIHNob3csIGJyaW5naW5nIHlvdSB0aGUgd2FybXRoLCBodW1vciwgYW5kIGZhbWlseSB2YWx1ZXMgdGhhdCBtYWRlIGl0IHNvIHNwZWNpYWwuJyxcbiAgICBcbiAgICAvLyBDaGF0XG4gICAgJ2NoYXQudGl0bGUnOiAnQ2hvdWZsaSBIYWwgQ2hhdCcsXG4gICAgJ2NoYXQuc3VidGl0bGUnOiAnQ2hhdCB3aXRoIHlvdXIgZnJpZW5kbHkgQUkgY29tcGFuaW9uIScsXG4gICAgJ2NoYXQucGxhY2Vob2xkZXInOiAnVHlwZSB5b3VyIG1lc3NhZ2UgaGVyZSDwn5KsJyxcbiAgICAnY2hhdC5zZW5kJzogJ1NlbmQnLFxuICAgICdjaGF0LnR5cGluZyc6ICd0eXBpbmcuLi4nLFxuICAgICdjaGF0LnByZXNzRW50ZXInOiAnUHJlc3MgRW50ZXIgdG8gc2VuZCcsXG4gICAgXG4gICAgLy8gQXV0aFxuICAgICdhdXRoLmVtYWlsJzogJ0VtYWlsJyxcbiAgICAnYXV0aC5wYXNzd29yZCc6ICdQYXNzd29yZCcsXG4gICAgJ2F1dGguY29uZmlybVBhc3N3b3JkJzogJ0NvbmZpcm0gUGFzc3dvcmQnLFxuICAgICdhdXRoLmZ1bGxOYW1lJzogJ0Z1bGwgTmFtZScsXG4gICAgJ2F1dGguc2lnbkluJzogJ1NpZ24gSW4nLFxuICAgICdhdXRoLnNpZ25VcCc6ICdTaWduIFVwJyxcbiAgICAnYXV0aC53ZWxjb21lQmFjayc6ICdXZWxjb21lIGJhY2snLFxuICAgICdhdXRoLmpvaW5GYW1pbHknOiAnSm9pbiBvdXIgZmFtaWx5IScsXG4gICAgJ2F1dGguY3JlYXRlQWNjb3VudCc6ICdDcmVhdGUgeW91ciBhY2NvdW50IHRvIHN0YXJ0IGNoYXR0aW5nJyxcbiAgICAnYXV0aC5hbHJlYWR5SGF2ZUFjY291bnQnOiAnQWxyZWFkeSBoYXZlIGFuIGFjY291bnQ/JyxcbiAgICAnYXV0aC5kb250SGF2ZUFjY291bnQnOiAnRG9uXFwndCBoYXZlIGFuIGFjY291bnQ/JyxcbiAgICAnYXV0aC5zaWduSW5IZXJlJzogJ1NpZ24gaW4gaGVyZScsXG4gICAgJ2F1dGguc2lnblVwSGVyZSc6ICdTaWduIHVwIGhlcmUnLFxuICAgICdhdXRoLmJhY2tUb0hvbWUnOiAnQmFjayB0byBIb21lJyxcbiAgICBcbiAgICAvLyBDb21tb25cbiAgICAnY29tbW9uLmNsb3NlJzogJ0Nsb3NlJyxcbiAgICAnY29tbW9uLmxvYWRpbmcnOiAnTG9hZGluZy4uLicsXG4gICAgJ2NvbW1vbi53ZWxjb21lJzogJ1dlbGNvbWUhJ1xuICB9LFxuICBhcjoge1xuICAgIC8vIE5hdmlnYXRpb25cbiAgICAnbmF2LmhvbWUnOiAn2KfZhNix2KbZitiz2YrYqScsXG4gICAgJ25hdi5jaGF0JzogJ9iv2LHYr9i02KknLFxuICAgICduYXYubG9naW4nOiAn2K/YrtmI2YQnLFxuICAgICduYXYuc2lnbnVwJzogJ9iq2LPYrNmK2YQnLFxuICAgIFxuICAgIC8vIEhvbWUgcGFnZVxuICAgICdob21lLnRpdGxlJzogJ9i02YjZgdmE2Yog2K3ZhCcsXG4gICAgJ2hvbWUuc3VidGl0bGUnOiAn2LHZiNio2YjYqiDYr9ix2K/YtNipINiw2YPZiiDZhdiz2KrZiNit2Ykg2YXZhiDYp9mE2YXYs9mE2LPZhCDYp9mE2KrZiNmG2LPZiiDYp9mE2LTZh9mK2LEnLFxuICAgICdob21lLmRlc2NyaXB0aW9uJzogJ9iv2LHYr9i0INmF2Lkg2KfZhNiw2YPYp9ihINin2YTYp9i12LfZhtin2LnZiiDYp9mE2YXYs9iq2YjYrdmJINmF2YYg2KfZhNmF2LPZhNiz2YQg2KfZhNiq2YjZhtiz2Yog2KfZhNi02YfZitixJyxcbiAgICAnaG9tZS5zdGFydENoYXQnOiAn2KfYqNiv2KMg2KfZhNmF2K3Yp9iv2KvYqScsXG4gICAgJ2hvbWUuc2lnbkluJzogJ9iv2K7ZiNmEJyxcbiAgICAnaG9tZS5zaWduVXBGcmVlJzogJ9iq2LPYrNmK2YQg2YXYrNin2YbZiicsXG4gICAgJ2hvbWUudHJ5V2l0aG91dEFjY291bnQnOiAn2KzYsdioINio2K/ZiNmGINit2LPYp9ioJyxcbiAgICAnaG9tZS5yZWFkeVRvQ2hhdCc6ICfZhdiz2KrYudivINmE2YTYr9ix2K/YtNip2J8nLFxuICAgICdob21lLmpvaW5Vc2Vycyc6ICfYp9mG2LbZhSDZhNii2YTYp9mBINin2YTZhdiz2KrYrtiv2YXZitmGINin2YTYsNmK2YYg2YrYs9iq2YXYqti52YjZhiDYqNin2YTZhdit2KfYr9ir2KfYqiDZhdi5INix2YjYqNmI2Kog2LTZiNmB2YTZiiDYrdmEJyxcbiAgICBcbiAgICAvLyBGZWF0dXJlc1xuICAgICdmZWF0dXJlcy5haVBvd2VyZWQnOiAn2K/Ysdiv2LTYqSDYqNin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52YonLFxuICAgICdmZWF0dXJlcy5haURlc2NyaXB0aW9uJzogJ9iv2LHYr9i0INmF2Lkg2LDZg9in2KEg2KfYtdi32YbYp9i52Yog2YrZgdmH2YUg2KfZhNir2YLYp9mB2Kkg2KfZhNiq2YjZhtiz2YrYqSDZiNix2YjYrSDYtNmI2YHZhNmKINit2YQnLFxuICAgICdmZWF0dXJlcy50dW5pc2lhblNwaXJpdCc6ICfYp9mE2LHZiNitINin2YTYqtmI2YbYs9mK2KknLFxuICAgICdmZWF0dXJlcy50dW5pc2lhbkRlc2NyaXB0aW9uJzogJ9in2K7Yqtio2LEg2K/ZgdihINmI2YHZg9in2YfYqSDYp9mE2K3Zitin2Kkg2KfZhNi52KfYptmE2YrYqSDYp9mE2KrZiNmG2LPZitipINmF2YYg2K7ZhNin2YQg2LHZiNio2YjYqiDYp9mE2K/Ysdiv2LTYqScsXG4gICAgJ2ZlYXR1cmVzLmZhbWlseUZyaWVuZGx5JzogJ9mF2YbYp9iz2Kgg2YTZhNi52KfYptmE2KknLFxuICAgICdmZWF0dXJlcy5mYW1pbHlEZXNjcmlwdGlvbic6ICfZhdir2YQg2KfZhNmF2LPZhNiz2YQg2KrZhdin2YXYp9mL2Iwg2K/Ysdiv2LTYqtmG2Kcg2YXZhtin2LPYqNipINmE2YTYudin2KbZhNipINmI2YXZhNmK2KbYqSDYqNin2YTYt9in2YLYqSDYp9mE2KXZitis2KfYqNmK2KknLFxuICAgIFxuICAgIC8vIEFib3V0XG4gICAgJ2Fib3V0LnRpdGxlJzogJ9i52YYg2LTZiNmB2YTZiiDYrdmEJyxcbiAgICAnYWJvdXQuZGVzY3JpcHRpb24xJzogJ9i02YjZgdmE2Yog2K3ZhCDZhdiz2YTYs9mEINiq2YjZhtiz2Yog2YPZiNmF2YrYr9mKINmF2K3YqNmI2Kgg2KzZhNioINin2YTYttit2YMg2YjYp9mE2YHYsditINmE2YTYudin2KbZhNin2Kog2YHZiiDYqtmI2YbYsyDZiNin2YTYudin2YTZhSDYp9mE2LnYsdio2YouJyxcbiAgICAnYWJvdXQuZGVzY3JpcHRpb24yJzogJ9mK2KzYs9ivINix2YjYqNmI2Kog2KfZhNiv2LHYr9i02Kkg2KfZhNiu2KfYtSDYqNmG2Kcg2LHZiNitINmH2LDYpyDYp9mE2LnYsdi2INin2YTYsdin2KbYudiMINmI2YrZgtiv2YUg2YTZgyDYp9mE2K/ZgdihINmI2KfZhNmB2YPYp9mH2Kkg2YjYp9mE2YLZitmFINin2YTYudin2KbZhNmK2Kkg2KfZhNiq2Yog2KzYudmE2KrZhyDZhdmF2YrYstin2Ysg2KzYr9in2YsuJyxcbiAgICBcbiAgICAvLyBDaGF0XG4gICAgJ2NoYXQudGl0bGUnOiAn2K/Ysdiv2LTYqSDYtNmI2YHZhNmKINit2YQnLFxuICAgICdjaGF0LnN1YnRpdGxlJzogJ9iv2LHYr9i0INmF2Lkg2LHZgdmK2YLZgyDYp9mE2LDZg9mKINin2YTZiNiv2YjYryEnLFxuICAgICdjaGF0LnBsYWNlaG9sZGVyJzogJ9in2YPYqtioINix2LPYp9mE2KrZgyDZh9mG2Kcg8J+SrCcsXG4gICAgJ2NoYXQuc2VuZCc6ICfYpdix2LPYp9mEJyxcbiAgICAnY2hhdC50eXBpbmcnOiAn2YrZg9iq2KguLi4nLFxuICAgICdjaGF0LnByZXNzRW50ZXInOiAn2KfYtti62LcgRW50ZXIg2YTZhNil2LHYs9in2YQnLFxuICAgIFxuICAgIC8vIEF1dGhcbiAgICAnYXV0aC5lbWFpbCc6ICfYp9mE2KjYsdmK2K8g2KfZhNil2YTZg9iq2LHZiNmG2YonLFxuICAgICdhdXRoLnBhc3N3b3JkJzogJ9mD2YTZhdipINin2YTZhdix2YjYsScsXG4gICAgJ2F1dGguY29uZmlybVBhc3N3b3JkJzogJ9iq2KPZg9mK2K8g2YPZhNmF2Kkg2KfZhNmF2LHZiNixJyxcbiAgICAnYXV0aC5mdWxsTmFtZSc6ICfYp9mE2KfYs9mFINin2YTZg9in2YXZhCcsXG4gICAgJ2F1dGguc2lnbkluJzogJ9iv2K7ZiNmEJyxcbiAgICAnYXV0aC5zaWduVXAnOiAn2KrYs9is2YrZhCcsXG4gICAgJ2F1dGgud2VsY29tZUJhY2snOiAn2YXYsdit2KjYp9mLINio2LnZiNiv2KrZgycsXG4gICAgJ2F1dGguam9pbkZhbWlseSc6ICfYp9mG2LbZhSDZhNi52KfYptmE2KrZhtinIScsXG4gICAgJ2F1dGguY3JlYXRlQWNjb3VudCc6ICfYo9mG2LTYpiDYrdiz2KfYqNmDINmE2KjYr9ihINin2YTZhdit2KfYr9ir2KknLFxuICAgICdhdXRoLmFscmVhZHlIYXZlQWNjb3VudCc6ICfZhNiv2YrZgyDYrdiz2KfYqCDYqNin2YTZgdi52YTYnycsXG4gICAgJ2F1dGguZG9udEhhdmVBY2NvdW50JzogJ9mE2YrYsyDZhNiv2YrZgyDYrdiz2KfYqNifJyxcbiAgICAnYXV0aC5zaWduSW5IZXJlJzogJ9in2K/YrtmEINmH2YbYpycsXG4gICAgJ2F1dGguc2lnblVwSGVyZSc6ICfYs9is2YQg2YfZhtinJyxcbiAgICAnYXV0aC5iYWNrVG9Ib21lJzogJ9in2YTYudmI2K/YqSDZhNmE2LHYptmK2LPZitipJyxcbiAgICBcbiAgICAvLyBDb21tb25cbiAgICAnY29tbW9uLmNsb3NlJzogJ9il2LrZhNin2YInLFxuICAgICdjb21tb24ubG9hZGluZyc6ICfYrNin2LHZiiDYp9mE2KrYrdmF2YrZhC4uLicsXG4gICAgJ2NvbW1vbi53ZWxjb21lJzogJ9ij2YfZhNin2Ysg2YjYs9mH2YTYp9mLISdcbiAgfVxufTtcblxuY29uc3QgTGFuZ3VhZ2VDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxMYW5ndWFnZUNvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgZnVuY3Rpb24gTGFuZ3VhZ2VQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIGNvbnN0IFtsYW5ndWFnZSwgc2V0TGFuZ3VhZ2VdID0gdXNlU3RhdGU8TGFuZ3VhZ2U+KCdlbicpO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRNb3VudGVkKHRydWUpO1xuICAgIC8vIENoZWNrIGlmIHRoZXJlJ3MgYSBzYXZlZCBsYW5ndWFnZSBwcmVmZXJlbmNlXG4gICAgY29uc3Qgc2F2ZWRMYW5ndWFnZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdjaG91ZmxpLWxhbmd1YWdlJykgYXMgTGFuZ3VhZ2U7XG4gICAgaWYgKHNhdmVkTGFuZ3VhZ2UgJiYgKHNhdmVkTGFuZ3VhZ2UgPT09ICdlbicgfHwgc2F2ZWRMYW5ndWFnZSA9PT0gJ2FyJykpIHtcbiAgICAgIHNldExhbmd1YWdlKHNhdmVkTGFuZ3VhZ2UpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKG1vdW50ZWQpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdjaG91ZmxpLWxhbmd1YWdlJywgbGFuZ3VhZ2UpO1xuICAgICAgLy8gQXBwbHkgUlRMIGZvciBBcmFiaWNcbiAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5kaXIgPSBsYW5ndWFnZSA9PT0gJ2FyJyA/ICdydGwnIDogJ2x0cic7XG4gICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQubGFuZyA9IGxhbmd1YWdlO1xuICAgIH1cbiAgfSwgW2xhbmd1YWdlLCBtb3VudGVkXSk7XG5cbiAgY29uc3QgdG9nZ2xlTGFuZ3VhZ2UgPSAoKSA9PiB7XG4gICAgc2V0TGFuZ3VhZ2UocHJldiA9PiBwcmV2ID09PSAnZW4nID8gJ2FyJyA6ICdlbicpO1xuICB9O1xuXG4gIGNvbnN0IHQgPSAoa2V5OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAgIHJldHVybiB0cmFuc2xhdGlvbnNbbGFuZ3VhZ2VdW2tleSBhcyBrZXlvZiB0eXBlb2YgdHJhbnNsYXRpb25zWydlbiddXSB8fCBrZXk7XG4gIH07XG5cbiAgaWYgKCFtb3VudGVkKSB7XG4gICAgcmV0dXJuIG51bGw7IC8vIFByZXZlbnQgaHlkcmF0aW9uIG1pc21hdGNoXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxMYW5ndWFnZUNvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3sgbGFuZ3VhZ2UsIHRvZ2dsZUxhbmd1YWdlLCB0IH19PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTGFuZ3VhZ2VDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlTGFuZ3VhZ2UoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KExhbmd1YWdlQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUxhbmd1YWdlIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBMYW5ndWFnZVByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidHJhbnNsYXRpb25zIiwiZW4iLCJhciIsIkxhbmd1YWdlQ29udGV4dCIsInVuZGVmaW5lZCIsIkxhbmd1YWdlUHJvdmlkZXIiLCJjaGlsZHJlbiIsImxhbmd1YWdlIiwic2V0TGFuZ3VhZ2UiLCJtb3VudGVkIiwic2V0TW91bnRlZCIsInNhdmVkTGFuZ3VhZ2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwic2V0SXRlbSIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiZGlyIiwibGFuZyIsInRvZ2dsZUxhbmd1YWdlIiwicHJldiIsInQiLCJrZXkiLCJQcm92aWRlciIsInZhbHVlIiwidXNlTGFuZ3VhZ2UiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/contexts/LanguageContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/contexts/ThemeContext.tsx":
/*!*******************************************!*\
  !*** ./src/app/contexts/ThemeContext.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dark'); // Default to dark theme\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            setMounted(true);\n            // Check if there's a saved theme preference\n            const savedTheme = localStorage.getItem('choufli-theme');\n            if (savedTheme) {\n                setTheme(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (mounted) {\n                localStorage.setItem('choufli-theme', theme);\n                // Apply theme to document\n                document.documentElement.setAttribute('data-theme', theme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setTheme((prev)=>prev === 'dark' ? 'light' : 'dark');\n    };\n    if (!mounted) {\n        return null; // Prevent hydration mismatch\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();