'use client';

import { useState } from 'react';

interface Character {
  name: string;
  nameArabic: string;
  emoji: string;
  description: string;
  quote: string;
  color: string;
}

const choufliCharacters: Character[] = [
  {
    name: "<PERSON>",
    nameArabic: "سي شبير",
    emoji: "👨‍💼",
    description: "The wise and loving father figure of the family",
    quote: "كل مشكلة وإلها حل - Every problem has a solution",
    color: "var(--choufli-primary)"
  },
  {
    name: "<PERSON><PERSON>",
    nameArabic: "لالة فاطمة",
    emoji: "👩‍🍳",
    description: "The caring mother who keeps the family together",
    quote: "الأكل على قد المحبة - Food tastes better with love",
    color: "var(--choufli-secondary)"
  },
  {
    name: "<PERSON><PERSON>",
    nameArabic: "صلاح",
    emoji: "👨‍🎓",
    description: "The ambitious son with big dreams",
    quote: "التعليم نور - Education is light",
    color: "var(--choufli-accent)"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    nameArabic: "نجلة",
    emoji: "👩‍💻",
    description: "The modern daughter balancing tradition and progress",
    quote: "المرأة نصف المجتمع - Women are half of society",
    color: "var(--choufli-warm)"
  },
  {
    name: "Jaddou",
    nameArabic: "جدو",
    emoji: "👴",
    description: "The wise grandfather with stories from the past",
    quote: "الخبرة خير من ألف كتاب - Experience is better than a thousand books",
    color: "var(--choufli-gold)"
  },
  {
    name: "Teta",
    nameArabic: "تيتة",
    emoji: "👵",
    description: "The loving grandmother with traditional wisdom",
    quote: "البيت بلا جدة كالحديقة بلا ورد - A home without grandmother is like a garden without flowers",
    color: "var(--choufli-deep)"
  }
];

export default function ChoufliCharacters() {
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);

  return (
    <div className="w-full">
      <h3 className="text-2xl font-bold text-center mb-8" style={{ color: 'var(--choufli-primary)' }}>
        Meet the Choufli Hal Family / تعرف على عائلة شوفلي حل
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        {choufliCharacters.map((character, index) => (
          <div
            key={index}
            className="chat-container p-4 rounded-xl text-center cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
            onClick={() => setSelectedCharacter(character)}
            style={{
              borderColor: character.color,
              borderWidth: '2px'
            }}
          >
            <div className="text-4xl mb-2">{character.emoji}</div>
            <h4 className="font-bold text-sm" style={{ color: character.color }}>
              {character.name}
            </h4>
            <p className="text-xs opacity-75" style={{ color: 'var(--foreground)' }}>
              {character.nameArabic}
            </p>
          </div>
        ))}
      </div>

      {selectedCharacter && (
        <div className="chat-container p-6 rounded-2xl shadow-2xl">
          <div className="flex items-center gap-4 mb-4">
            <div className="text-6xl">{selectedCharacter.emoji}</div>
            <div>
              <h3 className="text-2xl font-bold" style={{ color: selectedCharacter.color }}>
                {selectedCharacter.name}
              </h3>
              <p className="text-lg opacity-75" style={{ color: 'var(--foreground)' }}>
                {selectedCharacter.nameArabic}
              </p>
            </div>
          </div>
          
          <p className="text-lg mb-4" style={{ color: 'var(--foreground)' }}>
            {selectedCharacter.description}
          </p>
          
          <div className="p-4 rounded-xl" style={{ 
            backgroundColor: 'rgba(139, 69, 19, 0.1)',
            borderLeft: `4px solid ${selectedCharacter.color}`
          }}>
            <p className="text-lg font-medium italic" style={{ color: selectedCharacter.color }}>
              "{selectedCharacter.quote}"
            </p>
          </div>
          
          <button
            onClick={() => setSelectedCharacter(null)}
            className="mt-4 px-4 py-2 rounded-lg text-white transition-all duration-300 hover:scale-105"
            style={{ background: selectedCharacter.color }}
          >
            Close / إغلاق
          </button>
        </div>
      )}
    </div>
  );
}
