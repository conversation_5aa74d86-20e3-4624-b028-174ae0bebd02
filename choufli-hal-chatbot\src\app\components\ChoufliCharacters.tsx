'use client';

import { useState } from 'react';

interface Character {
  name: string;
  nameArabic: string;
  emoji: string;
  description: string;
  quote: string;
  color: string;
}

const choufli<PERSON>haracters: Character[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    nameArabic: "شب<PERSON><PERSON>",
    emoji: "👨‍🔧",
    description: "The main character, a simple and honest man who works hard to support his family",
    quote: "الحمد لله على كل حال - Thank God for everything",
    color: "var(--choufli-primary)"
  },
  {
    name: "<PERSON><PERSON>",
    nameArabic: "ناجت",
    emoji: "👩‍🏠",
    description: "<PERSON><PERSON><PERSON>'s wife, a devoted mother and housewife who manages the household",
    quote: "الصبر مفتاح الفرج - Patience is the key to relief",
    color: "var(--choufli-secondary)"
  },
  {
    name: "<PERSON><PERSON>",
    nameArabic: "صلاح",
    emoji: "👨‍🎓",
    description: "<PERSON><PERSON><PERSON>'s eldest son, a university student with big dreams and ambitions",
    quote: "العلم نور والجهل ظلام - Knowledge is light and ignorance is darkness",
    color: "var(--choufli-accent)"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    nameArabic: "نجلة",
    emoji: "👩‍🎨",
    description: "Choubir's daughter, a creative and modern young woman",
    quote: "المرأة نصف المجتمع - Women are half of society",
    color: "var(--choufli-warm)"
  },
  {
    name: "Hamma",
    nameArabic: "حمة",
    emoji: "👨‍🦳",
    description: "Choubir's father, the wise patriarch of the family with traditional values",
    quote: "احترم كبيرك يحترمك صغيرك - Respect your elders and the young will respect you",
    color: "var(--choufli-gold)"
  },
  {
    name: "Ommi Kalthoum",
    nameArabic: "أمي كلثوم",
    emoji: "👵",
    description: "Choubir's mother, the loving grandmother who holds the family together",
    quote: "البيت بلا أم كالجسم بلا روح - A home without a mother is like a body without a soul",
    color: "var(--choufli-deep)"
  },
  {
    name: "Hedi",
    nameArabic: "هادي",
    emoji: "👦",
    description: "Choubir's youngest son, a mischievous but lovable child",
    quote: "اللعب حق الطفل - Playing is a child's right",
    color: "var(--choufli-neon)"
  },
  {
    name: "Hajja Zeineb",
    nameArabic: "الحاجة زينب",
    emoji: "👩‍🦳",
    description: "The nosy but well-meaning neighbor who always has something to say",
    quote: "الجار قبل الدار - The neighbor comes before the house",
    color: "var(--choufli-cosmic)"
  }
];

export default function ChoufliCharacters() {
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);

  return (
    <div className="w-full">
      <h3 className="text-2xl font-bold text-center mb-8" style={{ color: 'var(--choufli-primary)' }}>
        Meet the Choufli Hal Family / تعرف على عائلة شوفلي حل
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-8">
        {choufliCharacters.map((character, index) => (
          <div
            key={index}
            className="chat-container p-4 rounded-xl text-center cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl"
            onClick={() => setSelectedCharacter(character)}
            style={{
              borderColor: character.color,
              borderWidth: '2px'
            }}
          >
            <div className="text-4xl mb-2">{character.emoji}</div>
            <h4 className="font-bold text-sm" style={{ color: character.color }}>
              {character.name}
            </h4>
            <p className="text-xs opacity-75" style={{ color: 'var(--foreground)' }}>
              {character.nameArabic}
            </p>
          </div>
        ))}
      </div>

      {selectedCharacter && (
        <div className="chat-container p-6 rounded-2xl shadow-2xl">
          <div className="flex items-center gap-4 mb-4">
            <div className="text-6xl">{selectedCharacter.emoji}</div>
            <div>
              <h3 className="text-2xl font-bold" style={{ color: selectedCharacter.color }}>
                {selectedCharacter.name}
              </h3>
              <p className="text-lg opacity-75" style={{ color: 'var(--foreground)' }}>
                {selectedCharacter.nameArabic}
              </p>
            </div>
          </div>
          
          <p className="text-lg mb-4" style={{ color: 'var(--foreground)' }}>
            {selectedCharacter.description}
          </p>
          
          <div className="p-4 rounded-xl" style={{ 
            backgroundColor: 'rgba(139, 69, 19, 0.1)',
            borderLeft: `4px solid ${selectedCharacter.color}`
          }}>
            <p className="text-lg font-medium italic" style={{ color: selectedCharacter.color }}>
              "{selectedCharacter.quote}"
            </p>
          </div>
          
          <button
            onClick={() => setSelectedCharacter(null)}
            className="mt-4 px-4 py-2 rounded-lg text-white transition-all duration-300 hover:scale-105"
            style={{ background: selectedCharacter.color }}
          >
            Close / إغلاق
          </button>
        </div>
      )}
    </div>
  );
}
