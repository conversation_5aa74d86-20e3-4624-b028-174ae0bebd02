'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'ar';

interface LanguageContextType {
  language: Language;
  toggleLanguage: () => void;
  t: (key: string) => string;
}

const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.chat': 'Chat',
    'nav.login': 'Login',
    'nav.signup': 'Sign Up',
    
    // Home page
    'home.title': 'Choufli Hal',
    'home.subtitle': 'AI Chatbot inspired by Tunisia\'s beloved sitcom',
    'home.description': 'Chat with an AI inspired by the famous Tunisian series',
    'home.startChat': 'Start Chatting',
    'home.signIn': 'Sign In',
    'home.signUpFree': 'Sign Up Free',
    'home.tryWithoutAccount': 'Try Without Account',
    'home.readyToChat': 'Ready to Chat?',
    'home.joinUsers': 'Join thousands of users who are already enjoying conversations with our Choufli Hal AI',
    
    // Features
    'features.aiPowered': 'AI Powered Chat',
    'features.aiDescription': 'Chat with an AI that understands Tunisian culture and the humor of Chouf<PERSON>',
    'features.tunisianSpirit': 'Tunisian Spirit',
    'features.tunisianDescription': 'Experience the warmth and humor of Tunisian family life through our chatbot',
    'features.familyFriendly': 'Family Friendly',
    'features.familyDescription': 'Just like the show, our chat is family-friendly and full of positive vibes',
    
    // About
    'about.title': 'About Choufli Hal',
    'about.description1': 'Choufli Hal is a beloved Tunisian sitcom that has brought laughter and joy to families across Tunisia and the Arab world.',
    'about.description2': 'Our AI chatbot captures the spirit of this wonderful show, bringing you the warmth, humor, and family values that made it so special.',
    
    // Chat
    'chat.title': 'Choufli Hal Chat',
    'chat.subtitle': 'Chat with your friendly AI companion!',
    'chat.placeholder': 'Type your message here 💬',
    'chat.send': 'Send',
    'chat.typing': 'typing...',
    'chat.pressEnter': 'Press Enter to send',
    
    // Auth
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.fullName': 'Full Name',
    'auth.signIn': 'Sign In',
    'auth.signUp': 'Sign Up',
    'auth.welcomeBack': 'Welcome back',
    'auth.joinFamily': 'Join our family!',
    'auth.createAccount': 'Create your account to start chatting',
    'auth.alreadyHaveAccount': 'Already have an account?',
    'auth.dontHaveAccount': 'Don\'t have an account?',
    'auth.signInHere': 'Sign in here',
    'auth.signUpHere': 'Sign up here',
    'auth.backToHome': 'Back to Home',
    
    // Common
    'common.close': 'Close',
    'common.loading': 'Loading...',
    'common.welcome': 'Welcome!'
  },
  ar: {
    // Navigation
    'nav.home': 'الرئيسية',
    'nav.chat': 'دردشة',
    'nav.login': 'دخول',
    'nav.signup': 'تسجيل',
    
    // Home page
    'home.title': 'شوفلي حل',
    'home.subtitle': 'روبوت دردشة ذكي مستوحى من المسلسل التونسي الشهير',
    'home.description': 'دردش مع الذكاء الاصطناعي المستوحى من المسلسل التونسي الشهير',
    'home.startChat': 'ابدأ المحادثة',
    'home.signIn': 'دخول',
    'home.signUpFree': 'تسجيل مجاني',
    'home.tryWithoutAccount': 'جرب بدون حساب',
    'home.readyToChat': 'مستعد للدردشة؟',
    'home.joinUsers': 'انضم لآلاف المستخدمين الذين يستمتعون بالمحادثات مع روبوت شوفلي حل',
    
    // Features
    'features.aiPowered': 'دردشة بالذكاء الاصطناعي',
    'features.aiDescription': 'دردش مع ذكاء اصطناعي يفهم الثقافة التونسية وروح شوفلي حل',
    'features.tunisianSpirit': 'الروح التونسية',
    'features.tunisianDescription': 'اختبر دفء وفكاهة الحياة العائلية التونسية من خلال روبوت الدردشة',
    'features.familyFriendly': 'مناسب للعائلة',
    'features.familyDescription': 'مثل المسلسل تماماً، دردشتنا مناسبة للعائلة ومليئة بالطاقة الإيجابية',
    
    // About
    'about.title': 'عن شوفلي حل',
    'about.description1': 'شوفلي حل مسلسل تونسي كوميدي محبوب جلب الضحك والفرح للعائلات في تونس والعالم العربي.',
    'about.description2': 'يجسد روبوت الدردشة الخاص بنا روح هذا العرض الرائع، ويقدم لك الدفء والفكاهة والقيم العائلية التي جعلته مميزاً جداً.',
    
    // Chat
    'chat.title': 'دردشة شوفلي حل',
    'chat.subtitle': 'دردش مع رفيقك الذكي الودود!',
    'chat.placeholder': 'اكتب رسالتك هنا 💬',
    'chat.send': 'إرسال',
    'chat.typing': 'يكتب...',
    'chat.pressEnter': 'اضغط Enter للإرسال',
    
    // Auth
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.confirmPassword': 'تأكيد كلمة المرور',
    'auth.fullName': 'الاسم الكامل',
    'auth.signIn': 'دخول',
    'auth.signUp': 'تسجيل',
    'auth.welcomeBack': 'مرحباً بعودتك',
    'auth.joinFamily': 'انضم لعائلتنا!',
    'auth.createAccount': 'أنشئ حسابك لبدء المحادثة',
    'auth.alreadyHaveAccount': 'لديك حساب بالفعل؟',
    'auth.dontHaveAccount': 'ليس لديك حساب؟',
    'auth.signInHere': 'ادخل هنا',
    'auth.signUpHere': 'سجل هنا',
    'auth.backToHome': 'العودة للرئيسية',
    
    // Common
    'common.close': 'إغلاق',
    'common.loading': 'جاري التحميل...',
    'common.welcome': 'أهلاً وسهلاً!'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Check if there's a saved language preference
    const savedLanguage = localStorage.getItem('choufli-language') as Language;
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
      setLanguage(savedLanguage);
    }
  }, []);

  useEffect(() => {
    if (mounted) {
      localStorage.setItem('choufli-language', language);
      // Apply RTL for Arabic
      document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = language;
    }
  }, [language, mounted]);

  const toggleLanguage = () => {
    setLanguage(prev => prev === 'en' ? 'ar' : 'en');
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations['en']] || key;
  };

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <LanguageContext.Provider value={{ language, toggleLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
