import type { ImageSegmentationInput, ImageSegmentationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type ImageSegmentationArgs = BaseArgs & ImageSegmentationInput;
/**
 * This task reads some image input and outputs the likelihood of classes & bounding boxes of detected objects.
 * Recommended model: facebook/detr-resnet-50-panoptic
 */
export declare function imageSegmentation(args: ImageSegmentationArgs, options?: Options): Promise<ImageSegmentationOutput>;
//# sourceMappingURL=imageSegmentation.d.ts.map