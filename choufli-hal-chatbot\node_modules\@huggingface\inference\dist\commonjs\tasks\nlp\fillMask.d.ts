import type { FillMaskInput, FillMaskOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type FillMaskArgs = BaseArgs & FillMaskInput;
/**
 * Tries to fill in a hole with a missing word (token to be precise). That’s the base task for BERT models.
 */
export declare function fillMask(args: FillMaskArgs, options?: Options): Promise<FillMaskOutput>;
//# sourceMappingURL=fillMask.d.ts.map