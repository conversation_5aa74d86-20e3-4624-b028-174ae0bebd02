# 🎭 <PERSON>ufli Hal Dynamic Chatbot

A dynamic AI-powered chatbot inspired by the beloved Tunisian sitcom "<PERSON><PERSON><PERSON>" (شوفلي حل). This chatbot combines the warmth and humor of the show with modern AI technology to create engaging conversations.

## ✨ Features

- 🤖 **Dynamic AI Responses** - Powered by Hugging Face (FREE!) for intelligent conversations
- 🌍 **Bilingual Support** - Arabic/English with RTL support
- 🌙 **Dark/Light Themes** - Galaxy-themed color palette with theme toggle
- 📱 **Responsive Design** - Works on desktop and mobile
- 🎭 **<PERSON><PERSON><PERSON> Hal Personality** - AI trained to embody the spirit of the show
- 🔄 **Fallback Mode** - Works even without API key (limited functionality)
- 💰 **FREE AI** - Uses Hugging Face's free API with generous limits

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Hugging Face API (FREE!)

To enable full dynamic AI capabilities:

1. **Get your FREE Hugging Face API key:**
   - Go to [Hugging Face Settings](https://huggingface.co/settings/tokens)
   - Create a new token (select "Read" access)
   - Copy your token

2. **Add your API key to `.env.local`:**
```bash
# Hugging Face API Configuration
HUGGINGFACE_API_KEY=your_actual_huggingface_token_here
```

**Note:** The chatbot will work without an API key but with limited static responses.

### 3. Run the Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 🎯 How It Works

### With Hugging Face API Key (Dynamic Mode) - FREE!
- ✅ Intelligent, context-aware responses
- ✅ Remembers conversation history
- ✅ Adapts to user's language and tone
- ✅ Embodies Choufli Hal characters' personalities
- ✅ Uses advanced language models like DialoGPT

### Without API Key (Fallback Mode)
- ⚡ Basic static responses
- ⚡ Still maintains Choufli Hal theme
- ⚡ Encourages API key setup

## 🎭 Choufli Hal Integration

The AI is trained with:
- **Character Knowledge** - Understands the show's characters and dynamics
- **Cultural Context** - Tunisian family values and traditions
- **Language Style** - Natural Arabic/English code-switching
- **Humor & Wisdom** - Incorporates the show's warmth and life lessons

## 🛠️ Technical Stack

- **Frontend**: Next.js 15, React, TypeScript
- **Styling**: Tailwind CSS with custom galaxy theme
- **AI**: Hugging Face Inference API (FREE!)
- **Models**: DialoGPT-medium, Llama-2, Mistral (configurable)
- **Internationalization**: Custom Arabic/English system
- **State Management**: React Context API

## 🤖 AI Models Available

The chatbot can use different Hugging Face models:

- **DialoGPT-medium** (default) - Fast, good for conversations
- **meta-llama/Llama-2-7b-chat-hf** - More advanced, slower
- **mistralai/Mistral-7B-Instruct-v0.1** - Good balance
- **microsoft/DialoGPT-large** - Better quality, slower

Change the model in `src/app/lib/huggingface.ts`.

## 🔧 API Endpoints

### POST /api/chat
Send a message to the chatbot:
```json
{
  "message": "مرحبا! Hello!"
}
```

Response:
```json
{
  "response": "Ahlan wa sahlan! مرحبا! How are you doing today?"
}
```

### GET /api/chat
Check API status and get setup information.

## 💡 Why Hugging Face?

- **FREE** - Generous free tier with no credit card required
- **Open Source** - Access to thousands of models
- **Fast** - Good performance for real-time chat
- **Reliable** - Stable API with good uptime
- **Community** - Large community and model ecosystem

## 🌟 Getting Your FREE API Key

1. Visit [huggingface.co](https://huggingface.co)
2. Sign up for a free account
3. Go to [Settings > Access Tokens](https://huggingface.co/settings/tokens)
4. Click "New token"
5. Give it a name (e.g., "Choufli Hal Chatbot")
6. Select "Read" access
7. Copy the token and add it to your `.env.local` file

That's it! No credit card, no payment required! 🎉

---

**Made with ❤️ for the Choufli Hal community - Now powered by FREE Hugging Face AI!**
