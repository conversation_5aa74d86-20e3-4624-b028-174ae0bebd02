import type { AudioClassificationT<PERSON><PERSON><PERSON><PERSON>, AudioToAudioTask<PERSON>elper, AutomaticSpeechRecognitionTaskHelper, ConversationalTaskHelper, DocumentQuestionAnsweringTaskHelper, FeatureExtractionTaskHelper, FillMaskTaskHelper, ImageClassificationTaskHelper, ImageSegmentationTaskHelper, ImageToImageTaskHelper, ImageToTextTaskHelper, ImageToVideoTaskHelper, ObjectDetectionTaskHelper, QuestionAnsweringTaskHelper, SentenceSimilarityTaskHelper, SummarizationTaskHelper, TableQuestionAnsweringTaskHelper, TabularClassificationTaskHelper, TabularRegressionTaskHelper, TaskProviderHelper, TextClassificationTaskHelper, TextGenerationTaskHelper, TextToAudioTaskHelper, TextToImageTaskHelper, TextToSpeechTaskHelper, TextToVideoTaskHelper, TokenClassificationTaskHelper, TranslationTask<PERSON>elper, VisualQuestionAnsweringTaskHelper, ZeroShotClassification<PERSON><PERSON><PERSON>elper, ZeroShotImageClassificationTaskHelper } from "../providers/providerHelper.js";
import type { InferenceProvider, InferenceProviderOrPolicy, InferenceTask } from "../types.js";
export declare const PROVIDERS: Record<InferenceProvider, Partial<Record<InferenceTask, TaskProviderHelper>>>;
/**
 * Get provider helper instance by name and task
 */
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "text-to-image"): TextToImageTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "conversational"): ConversationalTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "text-generation"): TextGenerationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "text-to-speech"): TextToSpeechTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "text-to-audio"): TextToAudioTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "automatic-speech-recognition"): AutomaticSpeechRecognitionTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "text-to-video"): TextToVideoTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "text-classification"): TextClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "question-answering"): QuestionAnsweringTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "audio-classification"): AudioClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "audio-to-audio"): AudioToAudioTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "fill-mask"): FillMaskTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "feature-extraction"): FeatureExtractionTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "image-classification"): ImageClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "image-segmentation"): ImageSegmentationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "document-question-answering"): DocumentQuestionAnsweringTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "image-to-text"): ImageToTextTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "object-detection"): ObjectDetectionTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "zero-shot-image-classification"): ZeroShotImageClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "zero-shot-classification"): ZeroShotClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "image-to-image"): ImageToImageTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "image-to-video"): ImageToVideoTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "sentence-similarity"): SentenceSimilarityTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "table-question-answering"): TableQuestionAnsweringTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "tabular-classification"): TabularClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "tabular-regression"): TabularRegressionTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "token-classification"): TokenClassificationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "translation"): TranslationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "summarization"): SummarizationTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: "visual-question-answering"): VisualQuestionAnsweringTaskHelper & TaskProviderHelper;
export declare function getProviderHelper(provider: InferenceProviderOrPolicy, task: InferenceTask | undefined): TaskProviderHelper;
//# sourceMappingURL=getProviderHelper.d.ts.map