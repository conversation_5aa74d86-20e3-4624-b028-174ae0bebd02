{"$id": "/inference/schemas/text-generation/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Text Generation Output.\n\nAuto-generated from TGI specs.\nFor more details, check out https://github.com/huggingface/huggingface.js/blob/main/packages/tasks/scripts/inference-tgi-import.ts.", "title": "TextGenerationOutput", "type": "object", "required": ["generated_text"], "properties": {"details": {"allOf": [{"$ref": "#/$defs/TextGenerationOutputDetails"}], "nullable": true}, "generated_text": {"type": "string", "example": "test"}}, "$defs": {"TextGenerationOutputDetails": {"type": "object", "required": ["finish_reason", "generated_tokens", "prefill", "tokens"], "properties": {"best_of_sequences": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputBestOfSequence"}, "nullable": true}, "finish_reason": {"$ref": "#/$defs/TextGenerationOutputFinishReason"}, "generated_tokens": {"type": "integer", "format": "int32", "example": 1, "minimum": 0}, "prefill": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputPrefillToken"}}, "seed": {"type": "integer", "format": "int64", "example": 42, "nullable": true, "minimum": 0}, "tokens": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputToken"}}, "top_tokens": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputToken"}}}}, "title": "TextGenerationOutputDetails"}, "TextGenerationOutputBestOfSequence": {"type": "object", "required": ["generated_text", "finish_reason", "generated_tokens", "prefill", "tokens"], "properties": {"finish_reason": {"$ref": "#/$defs/TextGenerationOutputFinishReason"}, "generated_text": {"type": "string", "example": "test"}, "generated_tokens": {"type": "integer", "format": "int32", "example": 1, "minimum": 0}, "prefill": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputPrefillToken"}}, "seed": {"type": "integer", "format": "int64", "example": 42, "nullable": true, "minimum": 0}, "tokens": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputToken"}}, "top_tokens": {"type": "array", "items": {"type": "array", "items": {"$ref": "#/$defs/TextGenerationOutputToken"}}}}, "title": "TextGenerationOutputBestOfSequence"}, "TextGenerationOutputFinishReason": {"type": "string", "enum": ["length", "eos_token", "stop_sequence"], "example": "Length", "title": "TextGenerationOutputFinishReason"}, "TextGenerationOutputPrefillToken": {"type": "object", "required": ["id", "text", "logprob"], "properties": {"id": {"type": "integer", "format": "int32", "example": 0, "minimum": 0}, "logprob": {"type": "number", "format": "float", "example": -0.34, "nullable": true}, "text": {"type": "string", "example": "test"}}, "title": "TextGenerationOutputPrefillToken"}, "TextGenerationOutputToken": {"type": "object", "required": ["id", "text", "logprob", "special"], "properties": {"id": {"type": "integer", "format": "int32", "example": 0, "minimum": 0}, "logprob": {"type": "number", "format": "float", "example": -0.34, "nullable": true}, "special": {"type": "boolean", "example": "false"}, "text": {"type": "string", "example": "test"}}, "title": "TextGenerationOutputToken"}}}