import type { SummarizationInput, SummarizationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type SummarizationArgs = BaseArgs & SummarizationInput;
/**
 * This task is well known to summarize longer text into shorter text. Be careful, some models have a maximum length of input. That means that the summary cannot handle full books for instance. Be careful when choosing your model.
 */
export declare function summarization(args: SummarizationArgs, options?: Options): Promise<SummarizationOutput>;
//# sourceMappingURL=summarization.d.ts.map