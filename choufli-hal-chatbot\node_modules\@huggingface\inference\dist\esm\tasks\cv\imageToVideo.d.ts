import type { ImageToVideoInput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type ImageToVideoArgs = BaseArgs & ImageToVideoInput;
/**
 * This task reads some text input and outputs an image.
 * Recommended model: Wan-AI/Wan2.1-I2V-14B-720P
 */
export declare function imageToVideo(args: ImageToVideoArgs, options?: Options): Promise<Blob>;
//# sourceMappingURL=imageToVideo.d.ts.map