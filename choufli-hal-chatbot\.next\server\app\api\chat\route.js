/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Enhanced responses inspired by Choufli Hal characters and situations\nconst choufliResponses = [\n    // General greetings and welcomes\n    \"Ahlan wa sahlan! مرحبا! How are you doing today? Just like Si Choubir always says, life is better with a smile! 😊\",\n    \"Ya ahla wa sahla! أهلا وسهلا! Welcome to our family chat! Si Choubir would be so happy to see you here. What brings you joy today?\",\n    \"Marhaba! مرحبا! Just like in Choufli 7al, every guest is family here. How can I help you today, my friend?\",\n    // Family and relationships\n    \"Ah, family! You know, in Choufli 7al, Choubir always said that family is everything. Whether it's dealing with Hedi's mischief or Ommi Kalthoum's wisdom, we stick together! Tell me about your family! 👨‍👩‍👧‍👦\",\n    \"That reminds me of Choubir and his wisdom! He always knew how to bring the family together, even when Hajja Zeineb was being nosy or when Slah had big dreams. What's your favorite family memory?\",\n    \"Just like in our beloved show, every family has its characters! Choubir the hardworking father, Najet the devoted mother, Hamma the wise grandfather - that's what makes life beautiful! 😄\",\n    // Humor and laughter\n    \"Haha! You know what Choubir would say? 'الضحك نص الصحة' - Laughter is half of health! Even when Hedi was causing trouble or Hajja Zeineb was gossiping, the family always found humor! 😂\",\n    \"That's hilarious! Reminds me of the funny situations in Choufli 7al. Remember how Choubir dealt with everyday problems with such humor? What makes you laugh the most?\",\n    \"Ya salam! That's funny! Just like when Hamma would tell his old stories or when Najla would tease her brothers - life is full of amusing moments! 🎭\",\n    // Wisdom and advice\n    \"You know what? Choubir always had the best advice. He'd say: 'كل مشكلة وإلها حل' - Every problem has a solution! Just like when he helped Slah with his studies or guided Najla. What's on your mind today?\",\n    \"That's a great question! In Choufli 7al, they taught us that talking things through with family helps. Whether it's Hamma's wisdom or Najet's patience, I'm here to listen! 💭\",\n    \"Wise words! Just like Ommi Kalthoum used to say, 'الصبر مفتاح الفرج' - Patience is the key to relief. Sometimes we just need to take things one step at a time.\",\n    // Tunisian culture and traditions\n    \"Ah, that takes me back to the beautiful Tunisian traditions shown in Choufli 7al! The warmth, the hospitality, the delicious food... What's your favorite Tunisian tradition? 🇹🇳\",\n    \"Mabrouk! You're touching on something beautiful! The show always celebrated our rich Tunisian culture. From couscous on Fridays to family gatherings, what do you love most about our traditions?\",\n    \"Barak Allah fik! That's wonderful! You know, Choufli 7al showed the world how beautiful Tunisian family life can be. The respect, the love, the togetherness... it's truly special! ✨\",\n    // Food and hospitality\n    \"Mmm, that sounds delicious! You know how in Choufli 7al, the kitchen was always the heart of the home? Si Choubir's wife made the best couscous! What's your favorite Tunisian dish? 🍽️\",\n    \"Ah, food! In our beloved show, every meal was a celebration. 'الأكل على قد المحبة' - Food tastes better when shared with love! Are you hungry? What would you like to eat?\",\n    // Daily life and work\n    \"That's interesting! Si Choubir always said that honest work is a blessing. Whether big or small, every job has its dignity. What do you do for work? 💼\",\n    \"You know what? In Choufli 7al, they showed us that life's simple pleasures are the most important. A cup of tea, a chat with neighbors, family time... What simple things make you happy?\",\n    // Encouragement and support\n    \"Don't worry, my friend! As Si Choubir used to say, 'ربي كريم' - God is generous! Every difficulty will pass. The family in Choufli 7al faced many challenges but always came through stronger! 💪\",\n    \"I understand! Life can be challenging sometimes. But remember what the show taught us - with family support and a positive attitude, we can overcome anything! I'm here for you! 🤗\",\n    \"That's the spirit! Just like in Choufli 7al, when we face problems with humor and family support, everything becomes easier. You're not alone in this! 🌟\"\n];\nasync function POST(request) {\n    try {\n        // Parse the request body\n        const { message } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Simulate processing time\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Enhanced response logic with more personalization\n        let response = choufliResponses[Math.floor(Math.random() * choufliResponses.length)];\n        // Personalized responses based on message content\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {\n            response = \"Ahlan wa sahlan! مرحبا وأهلا! Welcome to our Choufli 7al family chat! Just like Choubir's warm welcome and Najet's hospitality, I'm here to chat with you. How can I brighten your day? 😊✨\";\n        } else if (lowerMessage.includes('choufli') || lowerMessage.includes('7al') || lowerMessage.includes('شوفلي') || lowerMessage.includes('حل')) {\n            response = \"Ah, you mentioned Choufli 7al! 🎭 What a wonderful show! Choubir, Najet, Slah, Najla, Hedi, Hamma, and Ommi Kalthoum - they all taught us about family, love, and finding solutions together. What's your favorite moment from the series?\";\n        } else if (lowerMessage.includes('family') || lowerMessage.includes('عائلة') || lowerMessage.includes('أهل')) {\n            response = \"Family! 👨‍👩‍👧‍👦 Just like in Choufli 7al, family is everything! Choubir working hard, Najet taking care of everyone, Hamma sharing wisdom, Ommi Kalthoum's love - family sticks together. Tell me about your family!\";\n        } else if (lowerMessage.includes('food') || lowerMessage.includes('eat') || lowerMessage.includes('أكل') || lowerMessage.includes('طعام') || lowerMessage.includes('couscous')) {\n            response = \"Ah, food! 🍽️ In Choufli 7al, Najet's kitchen was always the heart of the home! Remember how the family gathered around the table? Even Hajja Zeineb would drop by for a meal! 'الأكل على قد المحبة' - Food tastes better when shared with love! What's your favorite dish?\";\n        } else if (lowerMessage.includes('problem') || lowerMessage.includes('help') || lowerMessage.includes('مشكلة') || lowerMessage.includes('مساعدة')) {\n            response = \"Don't worry, my friend! 🤗 As Choubir always said, 'كل مشكلة وإلها حل' - Every problem has a solution! Whether it was dealing with Hedi's school troubles or Slah's university challenges, the family always found a way. What's troubling you?\";\n        } else if (lowerMessage.includes('funny') || lowerMessage.includes('laugh') || lowerMessage.includes('joke') || lowerMessage.includes('ضحك') || lowerMessage.includes('مضحك')) {\n            response = \"Haha! 😂 You know what Si Choubir would say? 'الضحك نص الصحة' - Laughter is half of health! The family in Choufli 7al taught us that humor makes everything better. Even in tough times, they found reasons to smile. What makes you laugh the most?\";\n        } else if (lowerMessage.includes('tunisia') || lowerMessage.includes('tunisian') || lowerMessage.includes('تونس') || lowerMessage.includes('تونسي')) {\n            response = \"Ah, Tunisia! 🇹🇳 Our beautiful country! Choufli 7al showed the world the warmth and beauty of Tunisian culture - the hospitality, the traditions, the strong family bonds. From the medina to the coast, from couscous to makroudh, Tunisia is truly special! What do you love most about our beloved Tunisia?\";\n        } else if (lowerMessage.includes('sad') || lowerMessage.includes('tired') || lowerMessage.includes('difficult') || lowerMessage.includes('حزين') || lowerMessage.includes('تعبان') || lowerMessage.includes('صعب')) {\n            response = \"I understand, my friend. 💙 Life can be challenging sometimes. But remember what Si Choubir taught us - 'ربي كريم والأيام دول' - God is generous and these days will pass. In Choufli 7al, even in difficult moments, the family's love and support made everything better. You're not alone! What can I do to help cheer you up?\";\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response\n        });\n    } catch (error) {\n        console.error('Error in chat API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate response'\n        }, {\n            status: 500\n        });\n    }\n} // TODO: Replace with actual Google Gemini API implementation\n // When ready, install @google/generative-ai package and replace the mock implementation above\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();