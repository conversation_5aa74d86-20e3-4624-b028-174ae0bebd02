/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_huggingface__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/huggingface */ \"(rsc)/./src/app/lib/huggingface.ts\");\n\n\nasync function POST(request) {\n    try {\n        // Parse the request body\n        const { message } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Trim and validate message length\n        const trimmedMessage = message.trim();\n        if (trimmedMessage.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message cannot be empty'\n            }, {\n                status: 400\n            });\n        }\n        if (trimmedMessage.length > 1000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is too long. Please keep it under 1000 characters.'\n            }, {\n                status: 400\n            });\n        }\n        // Check if Hugging Face API key is configured\n        if (!process.env.HUGGINGFACE_API_KEY || process.env.HUGGINGFACE_API_KEY === 'your_huggingface_api_key_here') {\n            console.warn('Hugging Face API key not configured, using fallback responses');\n            // Fallback to static responses if API key is not configured\n            const fallbackResponses = [\n                \"Ahlan wa sahlan! مرحبا! I'd love to chat with you, but I need my Hugging Face API key to be configured first. It's FREE to get one! 😊\",\n                \"مرحبا بيك! Welcome! I'm still learning to be fully dynamic. For now, I can say that every conversation is a blessing! الحمد لله!\",\n                \"أهلا وسهلا! I'm here and ready to chat, but I need my full AI capabilities enabled. Get a FREE Hugging Face API key to unlock my potential! 🎭\",\n                \"Just like in Choufli Hal, 'كل مشكلة وإلها حل' - every problem has a solution! Get your free Hugging Face API key to chat with me properly! 🤖\"\n            ];\n            const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: randomResponse\n            });\n        }\n        // Generate dynamic response using Hugging Face\n        const response = await _lib_huggingface__WEBPACK_IMPORTED_MODULE_1__.choufliHalAI.generateResponse(trimmedMessage);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: response\n        });\n    } catch (error) {\n        console.error('Chat API Error:', error);\n        // Return a user-friendly error message\n        const errorResponse = \"أعتذر، أواجه مشكلة تقنية الآن. Sorry, I'm experiencing technical difficulties. Please try again in a moment! الحمد لله، سأعود قريباً! 🤖\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: errorResponse,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle GET requests (for testing)\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Choufli Hal Chat API is running! 🎭\",\n        status: \"active\",\n        aiProvider: \"Hugging Face (FREE)\",\n        endpoints: {\n            POST: \"/api/chat - Send a message to chat with the AI\"\n        },\n        example: {\n            method: \"POST\",\n            body: {\n                message: \"مرحبا! Hello!\"\n            }\n        },\n        setup: {\n            apiKey: \"Get your FREE Hugging Face API key from: https://huggingface.co/settings/tokens\",\n            envVar: \"HUGGINGFACE_API_KEY\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/lib/huggingface.ts":
/*!************************************!*\
  !*** ./src/app/lib/huggingface.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChoufliHalAI: () => (/* binding */ ChoufliHalAI),\n/* harmony export */   choufliHalAI: () => (/* binding */ choufliHalAI)\n/* harmony export */ });\n/* harmony import */ var _huggingface_inference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @huggingface/inference */ \"(rsc)/./node_modules/@huggingface/inference/dist/esm/index.js\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/app/lib/prompts.ts\");\n\n\n// Initialize Hugging Face client\nconst hf = new _huggingface_inference__WEBPACK_IMPORTED_MODULE_0__.HfInference(process.env.HUGGINGFACE_API_KEY);\nclass ChoufliHalAI {\n    // Alternative models you can try:\n    // 'meta-llama/Llama-2-7b-chat-hf' - More advanced but might be slower\n    // 'mistralai/Mistral-7B-Instruct-v0.1' - Good balance\n    // 'microsoft/DialoGPT-medium' - Fast and good for chat\n    constructor(){\n        this.conversationHistory = [];\n        this.model = 'microsoft/DialoGPT-medium' // Good for conversations\n        ;\n        // Initialize with system prompt\n        this.conversationHistory = [\n            {\n                role: 'system',\n                content: _prompts__WEBPACK_IMPORTED_MODULE_1__.CHOUFLI_HAL_SYSTEM_PROMPT\n            }\n        ];\n    }\n    async generateResponse(userMessage) {\n        try {\n            // Add user message to conversation history\n            this.conversationHistory.push({\n                role: 'user',\n                content: userMessage\n            });\n            // Keep conversation history manageable (last 8 messages + system prompt)\n            if (this.conversationHistory.length > 17) {\n                this.conversationHistory = [\n                    this.conversationHistory[0],\n                    ...this.conversationHistory.slice(-16) // Keep last 16 messages\n                ];\n            }\n            // Format conversation for Hugging Face\n            const conversationText = this.formatConversationForHF();\n            // Generate response using Hugging Face\n            const response = await hf.textGeneration({\n                model: this.model,\n                inputs: conversationText,\n                parameters: {\n                    max_new_tokens: 200,\n                    temperature: 0.8,\n                    top_p: 0.9,\n                    repetition_penalty: 1.1,\n                    return_full_text: false\n                }\n            });\n            let assistantResponse = response.generated_text?.trim() || '';\n            // Clean up the response\n            assistantResponse = this.cleanResponse(assistantResponse, userMessage);\n            // Fallback if response is empty or too short\n            if (!assistantResponse || assistantResponse.length < 10) {\n                assistantResponse = this.getFallbackResponse(userMessage);\n            }\n            // Add assistant response to conversation history\n            this.conversationHistory.push({\n                role: 'assistant',\n                content: assistantResponse\n            });\n            return assistantResponse;\n        } catch (error) {\n            console.error('Hugging Face API Error:', error);\n            // Fallback responses in case of API error\n            return this.getFallbackResponse(userMessage);\n        }\n    }\n    formatConversationForHF() {\n        // Format the conversation in a way that works well with Hugging Face models\n        let conversation = `${_prompts__WEBPACK_IMPORTED_MODULE_1__.CHOUFLI_HAL_SYSTEM_PROMPT}\\n\\n`;\n        // Add recent conversation history (excluding system prompt)\n        const recentMessages = this.conversationHistory.slice(1, -1); // Exclude system and current user message\n        for (const message of recentMessages){\n            if (message.role === 'user') {\n                conversation += `Human: ${message.content}\\n`;\n            } else if (message.role === 'assistant') {\n                conversation += `Assistant: ${message.content}\\n`;\n            }\n        }\n        // Add current user message\n        const currentUserMessage = this.conversationHistory[this.conversationHistory.length - 1];\n        conversation += `Human: ${currentUserMessage.content}\\nAssistant:`;\n        return conversation;\n    }\n    cleanResponse(response, userMessage) {\n        // Remove common artifacts from generated text\n        let cleaned = response.replace(/^(Assistant:|Human:|User:)/i, '') // Remove role prefixes\n        .replace(/\\n\\n+/g, '\\n') // Remove excessive newlines\n        .replace(/^\\s+|\\s+$/g, '') // Trim whitespace\n        .replace(/\\[.*?\\]/g, '') // Remove brackets\n        .replace(/\\*.*?\\*/g, '') // Remove asterisks\n        .replace(/Human:.*$/i, '') // Remove any trailing human input\n        .trim();\n        // Ensure response is not just repeating the user message\n        if (cleaned.toLowerCase().includes(userMessage.toLowerCase()) && cleaned.length < userMessage.length + 20) {\n            return this.getFallbackResponse(userMessage);\n        }\n        return cleaned;\n    }\n    getFallbackResponse(userMessage) {\n        const lowerMessage = userMessage.toLowerCase();\n        // Contextual fallback responses based on message content\n        if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {\n            const greetings = [\n                \"Ahlan wa sahlan! مرحبا! Welcome to our Choufli Hal family chat! How are you doing today? 😊\",\n                \"أهلا وسهلا! Just like in Choufli Hal, every guest is family here. What brings you joy today?\",\n                \"مرحبا بيك! Welcome! The family is always happy to see new faces. How can I help you today?\"\n            ];\n            return greetings[Math.floor(Math.random() * greetings.length)];\n        }\n        if (lowerMessage.includes('family') || lowerMessage.includes('عائلة')) {\n            return \"Family is everything! 👨‍👩‍👧‍👦 Just like in Choufli Hal, we believe that family sticks together through thick and thin. Tell me about your family!\";\n        }\n        if (lowerMessage.includes('help') || lowerMessage.includes('مساعدة')) {\n            return \"Don't worry, my friend! 🤗 As they say in Choufli Hal, 'كل مشكلة وإلها حل' - Every problem has a solution! What's troubling you?\";\n        }\n        // General fallback responses\n        const fallbacks = [\n            \"That's interesting! You know, in Choufli Hal, they always said that every conversation teaches us something new. Tell me more! 🎭\",\n            \"أهلا وسهلا! I'm here to chat with you just like the warm family from Choufli Hal. What would you like to talk about?\",\n            \"مرحبا! Just like the characters in our beloved show, I'm always ready for a good conversation. What's on your mind?\",\n            \"الحمد لله! Every chat is a blessing. In the spirit of Choufli Hal, let's make this conversation memorable! 😊\"\n        ];\n        return fallbacks[Math.floor(Math.random() * fallbacks.length)];\n    }\n    // Method to reset conversation (useful for new sessions)\n    resetConversation() {\n        this.conversationHistory = [\n            {\n                role: 'system',\n                content: _prompts__WEBPACK_IMPORTED_MODULE_1__.CHOUFLI_HAL_SYSTEM_PROMPT\n            }\n        ];\n    }\n    // Method to get conversation history (useful for debugging)\n    getConversationHistory() {\n        return [\n            ...this.conversationHistory\n        ];\n    }\n}\n// Create a singleton instance\nconst choufliHalAI = new ChoufliHalAI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xpYi9odWdnaW5nZmFjZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFEO0FBQ0M7QUFFdEQsaUNBQWlDO0FBQ2pDLE1BQU1FLEtBQUssSUFBSUYsK0RBQVdBLENBQUNHLFFBQVFDLEdBQUcsQ0FBQ0MsbUJBQW1CO0FBT25ELE1BQU1DO0lBR1gsa0NBQWtDO0lBQ2xDLHNFQUFzRTtJQUN0RSxzREFBc0Q7SUFDdEQsdURBQXVEO0lBRXZEQyxhQUFjO2FBUE5DLHNCQUFxQyxFQUFFO2FBQzlCQyxRQUFRLDRCQUE2Qix5QkFBeUI7O1FBTzdFLGdDQUFnQztRQUNoQyxJQUFJLENBQUNELG1CQUFtQixHQUFHO1lBQ3pCO2dCQUNFRSxNQUFNO2dCQUNOQyxTQUFTViwrREFBeUJBO1lBQ3BDO1NBQ0Q7SUFDSDtJQUVBLE1BQU1XLGlCQUFpQkMsV0FBbUIsRUFBbUI7UUFDM0QsSUFBSTtZQUNGLDJDQUEyQztZQUMzQyxJQUFJLENBQUNMLG1CQUFtQixDQUFDTSxJQUFJLENBQUM7Z0JBQzVCSixNQUFNO2dCQUNOQyxTQUFTRTtZQUNYO1lBRUEseUVBQXlFO1lBQ3pFLElBQUksSUFBSSxDQUFDTCxtQkFBbUIsQ0FBQ08sTUFBTSxHQUFHLElBQUk7Z0JBQ3hDLElBQUksQ0FBQ1AsbUJBQW1CLEdBQUc7b0JBQ3pCLElBQUksQ0FBQ0EsbUJBQW1CLENBQUMsRUFBRTt1QkFDeEIsSUFBSSxDQUFDQSxtQkFBbUIsQ0FBQ1EsS0FBSyxDQUFDLENBQUMsSUFBSSx3QkFBd0I7aUJBQ2hFO1lBQ0g7WUFFQSx1Q0FBdUM7WUFDdkMsTUFBTUMsbUJBQW1CLElBQUksQ0FBQ0MsdUJBQXVCO1lBRXJELHVDQUF1QztZQUN2QyxNQUFNQyxXQUFXLE1BQU1qQixHQUFHa0IsY0FBYyxDQUFDO2dCQUN2Q1gsT0FBTyxJQUFJLENBQUNBLEtBQUs7Z0JBQ2pCWSxRQUFRSjtnQkFDUkssWUFBWTtvQkFDVkMsZ0JBQWdCO29CQUNoQkMsYUFBYTtvQkFDYkMsT0FBTztvQkFDUEMsb0JBQW9CO29CQUNwQkMsa0JBQWtCO2dCQUNwQjtZQUNGO1lBRUEsSUFBSUMsb0JBQW9CVCxTQUFTVSxjQUFjLEVBQUVDLFVBQVU7WUFFM0Qsd0JBQXdCO1lBQ3hCRixvQkFBb0IsSUFBSSxDQUFDRyxhQUFhLENBQUNILG1CQUFtQmY7WUFFMUQsNkNBQTZDO1lBQzdDLElBQUksQ0FBQ2UscUJBQXFCQSxrQkFBa0JiLE1BQU0sR0FBRyxJQUFJO2dCQUN2RGEsb0JBQW9CLElBQUksQ0FBQ0ksbUJBQW1CLENBQUNuQjtZQUMvQztZQUVBLGlEQUFpRDtZQUNqRCxJQUFJLENBQUNMLG1CQUFtQixDQUFDTSxJQUFJLENBQUM7Z0JBQzVCSixNQUFNO2dCQUNOQyxTQUFTaUI7WUFDWDtZQUVBLE9BQU9BO1FBRVQsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBRXpDLDBDQUEwQztZQUMxQyxPQUFPLElBQUksQ0FBQ0QsbUJBQW1CLENBQUNuQjtRQUNsQztJQUNGO0lBRVFLLDBCQUFrQztRQUN4Qyw0RUFBNEU7UUFDNUUsSUFBSWlCLGVBQWUsR0FBR2xDLCtEQUF5QkEsQ0FBQyxJQUFJLENBQUM7UUFFckQsNERBQTREO1FBQzVELE1BQU1tQyxpQkFBaUIsSUFBSSxDQUFDNUIsbUJBQW1CLENBQUNRLEtBQUssQ0FBQyxHQUFHLENBQUMsSUFBSSwwQ0FBMEM7UUFFeEcsS0FBSyxNQUFNcUIsV0FBV0QsZUFBZ0I7WUFDcEMsSUFBSUMsUUFBUTNCLElBQUksS0FBSyxRQUFRO2dCQUMzQnlCLGdCQUFnQixDQUFDLE9BQU8sRUFBRUUsUUFBUTFCLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDL0MsT0FBTyxJQUFJMEIsUUFBUTNCLElBQUksS0FBSyxhQUFhO2dCQUN2Q3lCLGdCQUFnQixDQUFDLFdBQVcsRUFBRUUsUUFBUTFCLE9BQU8sQ0FBQyxFQUFFLENBQUM7WUFDbkQ7UUFDRjtRQUVBLDJCQUEyQjtRQUMzQixNQUFNMkIscUJBQXFCLElBQUksQ0FBQzlCLG1CQUFtQixDQUFDLElBQUksQ0FBQ0EsbUJBQW1CLENBQUNPLE1BQU0sR0FBRyxFQUFFO1FBQ3hGb0IsZ0JBQWdCLENBQUMsT0FBTyxFQUFFRyxtQkFBbUIzQixPQUFPLENBQUMsWUFBWSxDQUFDO1FBRWxFLE9BQU93QjtJQUNUO0lBRVFKLGNBQWNaLFFBQWdCLEVBQUVOLFdBQW1CLEVBQVU7UUFDbkUsOENBQThDO1FBQzlDLElBQUkwQixVQUFVcEIsU0FDWHFCLE9BQU8sQ0FBQywrQkFBK0IsSUFBSSx1QkFBdUI7U0FDbEVBLE9BQU8sQ0FBQyxVQUFVLE1BQU0sNEJBQTRCO1NBQ3BEQSxPQUFPLENBQUMsY0FBYyxJQUFJLGtCQUFrQjtTQUM1Q0EsT0FBTyxDQUFDLFlBQVksSUFBSSxrQkFBa0I7U0FDMUNBLE9BQU8sQ0FBQyxZQUFZLElBQUksbUJBQW1CO1NBQzNDQSxPQUFPLENBQUMsY0FBYyxJQUFJLGtDQUFrQztTQUM1RFYsSUFBSTtRQUVQLHlEQUF5RDtRQUN6RCxJQUFJUyxRQUFRRSxXQUFXLEdBQUdDLFFBQVEsQ0FBQzdCLFlBQVk0QixXQUFXLE9BQU9GLFFBQVF4QixNQUFNLEdBQUdGLFlBQVlFLE1BQU0sR0FBRyxJQUFJO1lBQ3pHLE9BQU8sSUFBSSxDQUFDaUIsbUJBQW1CLENBQUNuQjtRQUNsQztRQUVBLE9BQU8wQjtJQUNUO0lBRVFQLG9CQUFvQm5CLFdBQW1CLEVBQVU7UUFDdkQsTUFBTThCLGVBQWU5QixZQUFZNEIsV0FBVztRQUU1Qyx5REFBeUQ7UUFDekQsSUFBSUUsYUFBYUQsUUFBUSxDQUFDLFlBQVlDLGFBQWFELFFBQVEsQ0FBQyxTQUFTQyxhQUFhRCxRQUFRLENBQUMsWUFBWUMsYUFBYUQsUUFBUSxDQUFDLFNBQVM7WUFDcEksTUFBTUUsWUFBWTtnQkFDaEI7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUNELE9BQU9BLFNBQVMsQ0FBQ0MsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUtILFVBQVU3QixNQUFNLEVBQUU7UUFDaEU7UUFFQSxJQUFJNEIsYUFBYUQsUUFBUSxDQUFDLGFBQWFDLGFBQWFELFFBQVEsQ0FBQyxVQUFVO1lBQ3JFLE9BQU87UUFDVDtRQUVBLElBQUlDLGFBQWFELFFBQVEsQ0FBQyxXQUFXQyxhQUFhRCxRQUFRLENBQUMsV0FBVztZQUNwRSxPQUFPO1FBQ1Q7UUFFQSw2QkFBNkI7UUFDN0IsTUFBTU0sWUFBWTtZQUNoQjtZQUNBO1lBQ0E7WUFDQTtTQUNEO1FBRUQsT0FBT0EsU0FBUyxDQUFDSCxLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS0MsVUFBVWpDLE1BQU0sRUFBRTtJQUNoRTtJQUVBLHlEQUF5RDtJQUN6RGtDLG9CQUEwQjtRQUN4QixJQUFJLENBQUN6QyxtQkFBbUIsR0FBRztZQUN6QjtnQkFDRUUsTUFBTTtnQkFDTkMsU0FBU1YsK0RBQXlCQTtZQUNwQztTQUNEO0lBQ0g7SUFFQSw0REFBNEQ7SUFDNURpRCx5QkFBd0M7UUFDdEMsT0FBTztlQUFJLElBQUksQ0FBQzFDLG1CQUFtQjtTQUFDO0lBQ3RDO0FBQ0Y7QUFFQSw4QkFBOEI7QUFDdkIsTUFBTTJDLGVBQWUsSUFBSTdDLGVBQWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcam91aGFcXE9uZURyaXZlXFxCdXJlYXVcXHN0YXRpYyBib3QgY2hvdWZsaSA3YWxcXGNob3VmbGktaGFsLWNoYXRib3RcXHNyY1xcYXBwXFxsaWJcXGh1Z2dpbmdmYWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEhmSW5mZXJlbmNlIH0gZnJvbSAnQGh1Z2dpbmdmYWNlL2luZmVyZW5jZSc7XG5pbXBvcnQgeyBDSE9VRkxJX0hBTF9TWVNURU1fUFJPTVBUIH0gZnJvbSAnLi9wcm9tcHRzJztcblxuLy8gSW5pdGlhbGl6ZSBIdWdnaW5nIEZhY2UgY2xpZW50XG5jb25zdCBoZiA9IG5ldyBIZkluZmVyZW5jZShwcm9jZXNzLmVudi5IVUdHSU5HRkFDRV9BUElfS0VZKTtcblxuZXhwb3J0IGludGVyZmFjZSBDaGF0TWVzc2FnZSB7XG4gIHJvbGU6ICdzeXN0ZW0nIHwgJ3VzZXInIHwgJ2Fzc2lzdGFudCc7XG4gIGNvbnRlbnQ6IHN0cmluZztcbn1cblxuZXhwb3J0IGNsYXNzIENob3VmbGlIYWxBSSB7XG4gIHByaXZhdGUgY29udmVyc2F0aW9uSGlzdG9yeTogQ2hhdE1lc3NhZ2VbXSA9IFtdO1xuICBwcml2YXRlIHJlYWRvbmx5IG1vZGVsID0gJ21pY3Jvc29mdC9EaWFsb0dQVC1tZWRpdW0nOyAvLyBHb29kIGZvciBjb252ZXJzYXRpb25zXG4gIC8vIEFsdGVybmF0aXZlIG1vZGVscyB5b3UgY2FuIHRyeTpcbiAgLy8gJ21ldGEtbGxhbWEvTGxhbWEtMi03Yi1jaGF0LWhmJyAtIE1vcmUgYWR2YW5jZWQgYnV0IG1pZ2h0IGJlIHNsb3dlclxuICAvLyAnbWlzdHJhbGFpL01pc3RyYWwtN0ItSW5zdHJ1Y3QtdjAuMScgLSBHb29kIGJhbGFuY2VcbiAgLy8gJ21pY3Jvc29mdC9EaWFsb0dQVC1tZWRpdW0nIC0gRmFzdCBhbmQgZ29vZCBmb3IgY2hhdFxuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIC8vIEluaXRpYWxpemUgd2l0aCBzeXN0ZW0gcHJvbXB0XG4gICAgdGhpcy5jb252ZXJzYXRpb25IaXN0b3J5ID0gW1xuICAgICAge1xuICAgICAgICByb2xlOiAnc3lzdGVtJyxcbiAgICAgICAgY29udGVudDogQ0hPVUZMSV9IQUxfU1lTVEVNX1BST01QVFxuICAgICAgfVxuICAgIF07XG4gIH1cblxuICBhc3luYyBnZW5lcmF0ZVJlc3BvbnNlKHVzZXJNZXNzYWdlOiBzdHJpbmcpOiBQcm9taXNlPHN0cmluZz4ge1xuICAgIHRyeSB7XG4gICAgICAvLyBBZGQgdXNlciBtZXNzYWdlIHRvIGNvbnZlcnNhdGlvbiBoaXN0b3J5XG4gICAgICB0aGlzLmNvbnZlcnNhdGlvbkhpc3RvcnkucHVzaCh7XG4gICAgICAgIHJvbGU6ICd1c2VyJyxcbiAgICAgICAgY29udGVudDogdXNlck1lc3NhZ2VcbiAgICAgIH0pO1xuXG4gICAgICAvLyBLZWVwIGNvbnZlcnNhdGlvbiBoaXN0b3J5IG1hbmFnZWFibGUgKGxhc3QgOCBtZXNzYWdlcyArIHN5c3RlbSBwcm9tcHQpXG4gICAgICBpZiAodGhpcy5jb252ZXJzYXRpb25IaXN0b3J5Lmxlbmd0aCA+IDE3KSB7IC8vIDEgc3lzdGVtICsgMTYgbWVzc2FnZXMgKDggcGFpcnMpXG4gICAgICAgIHRoaXMuY29udmVyc2F0aW9uSGlzdG9yeSA9IFtcbiAgICAgICAgICB0aGlzLmNvbnZlcnNhdGlvbkhpc3RvcnlbMF0sIC8vIEtlZXAgc3lzdGVtIHByb21wdFxuICAgICAgICAgIC4uLnRoaXMuY29udmVyc2F0aW9uSGlzdG9yeS5zbGljZSgtMTYpIC8vIEtlZXAgbGFzdCAxNiBtZXNzYWdlc1xuICAgICAgICBdO1xuICAgICAgfVxuXG4gICAgICAvLyBGb3JtYXQgY29udmVyc2F0aW9uIGZvciBIdWdnaW5nIEZhY2VcbiAgICAgIGNvbnN0IGNvbnZlcnNhdGlvblRleHQgPSB0aGlzLmZvcm1hdENvbnZlcnNhdGlvbkZvckhGKCk7XG5cbiAgICAgIC8vIEdlbmVyYXRlIHJlc3BvbnNlIHVzaW5nIEh1Z2dpbmcgRmFjZVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBoZi50ZXh0R2VuZXJhdGlvbih7XG4gICAgICAgIG1vZGVsOiB0aGlzLm1vZGVsLFxuICAgICAgICBpbnB1dHM6IGNvbnZlcnNhdGlvblRleHQsXG4gICAgICAgIHBhcmFtZXRlcnM6IHtcbiAgICAgICAgICBtYXhfbmV3X3Rva2VuczogMjAwLFxuICAgICAgICAgIHRlbXBlcmF0dXJlOiAwLjgsXG4gICAgICAgICAgdG9wX3A6IDAuOSxcbiAgICAgICAgICByZXBldGl0aW9uX3BlbmFsdHk6IDEuMSxcbiAgICAgICAgICByZXR1cm5fZnVsbF90ZXh0OiBmYWxzZSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBsZXQgYXNzaXN0YW50UmVzcG9uc2UgPSByZXNwb25zZS5nZW5lcmF0ZWRfdGV4dD8udHJpbSgpIHx8ICcnO1xuXG4gICAgICAvLyBDbGVhbiB1cCB0aGUgcmVzcG9uc2VcbiAgICAgIGFzc2lzdGFudFJlc3BvbnNlID0gdGhpcy5jbGVhblJlc3BvbnNlKGFzc2lzdGFudFJlc3BvbnNlLCB1c2VyTWVzc2FnZSk7XG5cbiAgICAgIC8vIEZhbGxiYWNrIGlmIHJlc3BvbnNlIGlzIGVtcHR5IG9yIHRvbyBzaG9ydFxuICAgICAgaWYgKCFhc3Npc3RhbnRSZXNwb25zZSB8fCBhc3Npc3RhbnRSZXNwb25zZS5sZW5ndGggPCAxMCkge1xuICAgICAgICBhc3Npc3RhbnRSZXNwb25zZSA9IHRoaXMuZ2V0RmFsbGJhY2tSZXNwb25zZSh1c2VyTWVzc2FnZSk7XG4gICAgICB9XG5cbiAgICAgIC8vIEFkZCBhc3Npc3RhbnQgcmVzcG9uc2UgdG8gY29udmVyc2F0aW9uIGhpc3RvcnlcbiAgICAgIHRoaXMuY29udmVyc2F0aW9uSGlzdG9yeS5wdXNoKHtcbiAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXG4gICAgICAgIGNvbnRlbnQ6IGFzc2lzdGFudFJlc3BvbnNlXG4gICAgICB9KTtcblxuICAgICAgcmV0dXJuIGFzc2lzdGFudFJlc3BvbnNlO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0h1Z2dpbmcgRmFjZSBBUEkgRXJyb3I6JywgZXJyb3IpO1xuICAgICAgXG4gICAgICAvLyBGYWxsYmFjayByZXNwb25zZXMgaW4gY2FzZSBvZiBBUEkgZXJyb3JcbiAgICAgIHJldHVybiB0aGlzLmdldEZhbGxiYWNrUmVzcG9uc2UodXNlck1lc3NhZ2UpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgZm9ybWF0Q29udmVyc2F0aW9uRm9ySEYoKTogc3RyaW5nIHtcbiAgICAvLyBGb3JtYXQgdGhlIGNvbnZlcnNhdGlvbiBpbiBhIHdheSB0aGF0IHdvcmtzIHdlbGwgd2l0aCBIdWdnaW5nIEZhY2UgbW9kZWxzXG4gICAgbGV0IGNvbnZlcnNhdGlvbiA9IGAke0NIT1VGTElfSEFMX1NZU1RFTV9QUk9NUFR9XFxuXFxuYDtcbiAgICBcbiAgICAvLyBBZGQgcmVjZW50IGNvbnZlcnNhdGlvbiBoaXN0b3J5IChleGNsdWRpbmcgc3lzdGVtIHByb21wdClcbiAgICBjb25zdCByZWNlbnRNZXNzYWdlcyA9IHRoaXMuY29udmVyc2F0aW9uSGlzdG9yeS5zbGljZSgxLCAtMSk7IC8vIEV4Y2x1ZGUgc3lzdGVtIGFuZCBjdXJyZW50IHVzZXIgbWVzc2FnZVxuICAgIFxuICAgIGZvciAoY29uc3QgbWVzc2FnZSBvZiByZWNlbnRNZXNzYWdlcykge1xuICAgICAgaWYgKG1lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInKSB7XG4gICAgICAgIGNvbnZlcnNhdGlvbiArPSBgSHVtYW46ICR7bWVzc2FnZS5jb250ZW50fVxcbmA7XG4gICAgICB9IGVsc2UgaWYgKG1lc3NhZ2Uucm9sZSA9PT0gJ2Fzc2lzdGFudCcpIHtcbiAgICAgICAgY29udmVyc2F0aW9uICs9IGBBc3Npc3RhbnQ6ICR7bWVzc2FnZS5jb250ZW50fVxcbmA7XG4gICAgICB9XG4gICAgfVxuICAgIFxuICAgIC8vIEFkZCBjdXJyZW50IHVzZXIgbWVzc2FnZVxuICAgIGNvbnN0IGN1cnJlbnRVc2VyTWVzc2FnZSA9IHRoaXMuY29udmVyc2F0aW9uSGlzdG9yeVt0aGlzLmNvbnZlcnNhdGlvbkhpc3RvcnkubGVuZ3RoIC0gMV07XG4gICAgY29udmVyc2F0aW9uICs9IGBIdW1hbjogJHtjdXJyZW50VXNlck1lc3NhZ2UuY29udGVudH1cXG5Bc3Npc3RhbnQ6YDtcbiAgICBcbiAgICByZXR1cm4gY29udmVyc2F0aW9uO1xuICB9XG5cbiAgcHJpdmF0ZSBjbGVhblJlc3BvbnNlKHJlc3BvbnNlOiBzdHJpbmcsIHVzZXJNZXNzYWdlOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIC8vIFJlbW92ZSBjb21tb24gYXJ0aWZhY3RzIGZyb20gZ2VuZXJhdGVkIHRleHRcbiAgICBsZXQgY2xlYW5lZCA9IHJlc3BvbnNlXG4gICAgICAucmVwbGFjZSgvXihBc3Npc3RhbnQ6fEh1bWFuOnxVc2VyOikvaSwgJycpIC8vIFJlbW92ZSByb2xlIHByZWZpeGVzXG4gICAgICAucmVwbGFjZSgvXFxuXFxuKy9nLCAnXFxuJykgLy8gUmVtb3ZlIGV4Y2Vzc2l2ZSBuZXdsaW5lc1xuICAgICAgLnJlcGxhY2UoL15cXHMrfFxccyskL2csICcnKSAvLyBUcmltIHdoaXRlc3BhY2VcbiAgICAgIC5yZXBsYWNlKC9cXFsuKj9cXF0vZywgJycpIC8vIFJlbW92ZSBicmFja2V0c1xuICAgICAgLnJlcGxhY2UoL1xcKi4qP1xcKi9nLCAnJykgLy8gUmVtb3ZlIGFzdGVyaXNrc1xuICAgICAgLnJlcGxhY2UoL0h1bWFuOi4qJC9pLCAnJykgLy8gUmVtb3ZlIGFueSB0cmFpbGluZyBodW1hbiBpbnB1dFxuICAgICAgLnRyaW0oKTtcblxuICAgIC8vIEVuc3VyZSByZXNwb25zZSBpcyBub3QganVzdCByZXBlYXRpbmcgdGhlIHVzZXIgbWVzc2FnZVxuICAgIGlmIChjbGVhbmVkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXModXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKSkgJiYgY2xlYW5lZC5sZW5ndGggPCB1c2VyTWVzc2FnZS5sZW5ndGggKyAyMCkge1xuICAgICAgcmV0dXJuIHRoaXMuZ2V0RmFsbGJhY2tSZXNwb25zZSh1c2VyTWVzc2FnZSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGNsZWFuZWQ7XG4gIH1cblxuICBwcml2YXRlIGdldEZhbGxiYWNrUmVzcG9uc2UodXNlck1lc3NhZ2U6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3QgbG93ZXJNZXNzYWdlID0gdXNlck1lc3NhZ2UudG9Mb3dlckNhc2UoKTtcbiAgICBcbiAgICAvLyBDb250ZXh0dWFsIGZhbGxiYWNrIHJlc3BvbnNlcyBiYXNlZCBvbiBtZXNzYWdlIGNvbnRlbnRcbiAgICBpZiAobG93ZXJNZXNzYWdlLmluY2x1ZGVzKCdoZWxsbycpIHx8IGxvd2VyTWVzc2FnZS5pbmNsdWRlcygnaGknKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ9mF2LHYrdio2KcnKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ9ij2YfZhNinJykpIHtcbiAgICAgIGNvbnN0IGdyZWV0aW5ncyA9IFtcbiAgICAgICAgXCJBaGxhbiB3YSBzYWhsYW4hINmF2LHYrdio2KchIFdlbGNvbWUgdG8gb3VyIENob3VmbGkgSGFsIGZhbWlseSBjaGF0ISBIb3cgYXJlIHlvdSBkb2luZyB0b2RheT8g8J+YilwiLFxuICAgICAgICBcItij2YfZhNinINmI2LPZh9mE2KchIEp1c3QgbGlrZSBpbiBDaG91ZmxpIEhhbCwgZXZlcnkgZ3Vlc3QgaXMgZmFtaWx5IGhlcmUuIFdoYXQgYnJpbmdzIHlvdSBqb3kgdG9kYXk/XCIsXG4gICAgICAgIFwi2YXYsdit2KjYpyDYqNmK2YMhIFdlbGNvbWUhIFRoZSBmYW1pbHkgaXMgYWx3YXlzIGhhcHB5IHRvIHNlZSBuZXcgZmFjZXMuIEhvdyBjYW4gSSBoZWxwIHlvdSB0b2RheT9cIlxuICAgICAgXTtcbiAgICAgIHJldHVybiBncmVldGluZ3NbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZ3JlZXRpbmdzLmxlbmd0aCldO1xuICAgIH1cbiAgICBcbiAgICBpZiAobG93ZXJNZXNzYWdlLmluY2x1ZGVzKCdmYW1pbHknKSB8fCBsb3dlck1lc3NhZ2UuaW5jbHVkZXMoJ9i52KfYptmE2KknKSkge1xuICAgICAgcmV0dXJuIFwiRmFtaWx5IGlzIGV2ZXJ5dGhpbmchIPCfkajigI3wn5Gp4oCN8J+Rp+KAjfCfkaYgSnVzdCBsaWtlIGluIENob3VmbGkgSGFsLCB3ZSBiZWxpZXZlIHRoYXQgZmFtaWx5IHN0aWNrcyB0b2dldGhlciB0aHJvdWdoIHRoaWNrIGFuZCB0aGluLiBUZWxsIG1lIGFib3V0IHlvdXIgZmFtaWx5IVwiO1xuICAgIH1cbiAgICBcbiAgICBpZiAobG93ZXJNZXNzYWdlLmluY2x1ZGVzKCdoZWxwJykgfHwgbG93ZXJNZXNzYWdlLmluY2x1ZGVzKCfZhdiz2KfYudiv2KknKSkge1xuICAgICAgcmV0dXJuIFwiRG9uJ3Qgd29ycnksIG15IGZyaWVuZCEg8J+klyBBcyB0aGV5IHNheSBpbiBDaG91ZmxpIEhhbCwgJ9mD2YQg2YXYtNmD2YTYqSDZiNil2YTZh9inINit2YQnIC0gRXZlcnkgcHJvYmxlbSBoYXMgYSBzb2x1dGlvbiEgV2hhdCdzIHRyb3VibGluZyB5b3U/XCI7XG4gICAgfVxuICAgIFxuICAgIC8vIEdlbmVyYWwgZmFsbGJhY2sgcmVzcG9uc2VzXG4gICAgY29uc3QgZmFsbGJhY2tzID0gW1xuICAgICAgXCJUaGF0J3MgaW50ZXJlc3RpbmchIFlvdSBrbm93LCBpbiBDaG91ZmxpIEhhbCwgdGhleSBhbHdheXMgc2FpZCB0aGF0IGV2ZXJ5IGNvbnZlcnNhdGlvbiB0ZWFjaGVzIHVzIHNvbWV0aGluZyBuZXcuIFRlbGwgbWUgbW9yZSEg8J+OrVwiLFxuICAgICAgXCLYo9mH2YTYpyDZiNiz2YfZhNinISBJJ20gaGVyZSB0byBjaGF0IHdpdGggeW91IGp1c3QgbGlrZSB0aGUgd2FybSBmYW1pbHkgZnJvbSBDaG91ZmxpIEhhbC4gV2hhdCB3b3VsZCB5b3UgbGlrZSB0byB0YWxrIGFib3V0P1wiLFxuICAgICAgXCLZhdix2K3YqNinISBKdXN0IGxpa2UgdGhlIGNoYXJhY3RlcnMgaW4gb3VyIGJlbG92ZWQgc2hvdywgSSdtIGFsd2F5cyByZWFkeSBmb3IgYSBnb29kIGNvbnZlcnNhdGlvbi4gV2hhdCdzIG9uIHlvdXIgbWluZD9cIixcbiAgICAgIFwi2KfZhNit2YXYryDZhNmE2YchIEV2ZXJ5IGNoYXQgaXMgYSBibGVzc2luZy4gSW4gdGhlIHNwaXJpdCBvZiBDaG91ZmxpIEhhbCwgbGV0J3MgbWFrZSB0aGlzIGNvbnZlcnNhdGlvbiBtZW1vcmFibGUhIPCfmIpcIlxuICAgIF07XG4gICAgXG4gICAgcmV0dXJuIGZhbGxiYWNrc1tNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBmYWxsYmFja3MubGVuZ3RoKV07XG4gIH1cblxuICAvLyBNZXRob2QgdG8gcmVzZXQgY29udmVyc2F0aW9uICh1c2VmdWwgZm9yIG5ldyBzZXNzaW9ucylcbiAgcmVzZXRDb252ZXJzYXRpb24oKTogdm9pZCB7XG4gICAgdGhpcy5jb252ZXJzYXRpb25IaXN0b3J5ID0gW1xuICAgICAge1xuICAgICAgICByb2xlOiAnc3lzdGVtJyxcbiAgICAgICAgY29udGVudDogQ0hPVUZMSV9IQUxfU1lTVEVNX1BST01QVFxuICAgICAgfVxuICAgIF07XG4gIH1cblxuICAvLyBNZXRob2QgdG8gZ2V0IGNvbnZlcnNhdGlvbiBoaXN0b3J5ICh1c2VmdWwgZm9yIGRlYnVnZ2luZylcbiAgZ2V0Q29udmVyc2F0aW9uSGlzdG9yeSgpOiBDaGF0TWVzc2FnZVtdIHtcbiAgICByZXR1cm4gWy4uLnRoaXMuY29udmVyc2F0aW9uSGlzdG9yeV07XG4gIH1cbn1cblxuLy8gQ3JlYXRlIGEgc2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3QgY2hvdWZsaUhhbEFJID0gbmV3IENob3VmbGlIYWxBSSgpO1xuIl0sIm5hbWVzIjpbIkhmSW5mZXJlbmNlIiwiQ0hPVUZMSV9IQUxfU1lTVEVNX1BST01QVCIsImhmIiwicHJvY2VzcyIsImVudiIsIkhVR0dJTkdGQUNFX0FQSV9LRVkiLCJDaG91ZmxpSGFsQUkiLCJjb25zdHJ1Y3RvciIsImNvbnZlcnNhdGlvbkhpc3RvcnkiLCJtb2RlbCIsInJvbGUiLCJjb250ZW50IiwiZ2VuZXJhdGVSZXNwb25zZSIsInVzZXJNZXNzYWdlIiwicHVzaCIsImxlbmd0aCIsInNsaWNlIiwiY29udmVyc2F0aW9uVGV4dCIsImZvcm1hdENvbnZlcnNhdGlvbkZvckhGIiwicmVzcG9uc2UiLCJ0ZXh0R2VuZXJhdGlvbiIsImlucHV0cyIsInBhcmFtZXRlcnMiLCJtYXhfbmV3X3Rva2VucyIsInRlbXBlcmF0dXJlIiwidG9wX3AiLCJyZXBldGl0aW9uX3BlbmFsdHkiLCJyZXR1cm5fZnVsbF90ZXh0IiwiYXNzaXN0YW50UmVzcG9uc2UiLCJnZW5lcmF0ZWRfdGV4dCIsInRyaW0iLCJjbGVhblJlc3BvbnNlIiwiZ2V0RmFsbGJhY2tSZXNwb25zZSIsImVycm9yIiwiY29uc29sZSIsImNvbnZlcnNhdGlvbiIsInJlY2VudE1lc3NhZ2VzIiwibWVzc2FnZSIsImN1cnJlbnRVc2VyTWVzc2FnZSIsImNsZWFuZWQiLCJyZXBsYWNlIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImxvd2VyTWVzc2FnZSIsImdyZWV0aW5ncyIsIk1hdGgiLCJmbG9vciIsInJhbmRvbSIsImZhbGxiYWNrcyIsInJlc2V0Q29udmVyc2F0aW9uIiwiZ2V0Q29udmVyc2F0aW9uSGlzdG9yeSIsImNob3VmbGlIYWxBSSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/lib/huggingface.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/lib/prompts.ts":
/*!********************************!*\
  !*** ./src/app/lib/prompts.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHOUFLI_HAL_SYSTEM_PROMPT: () => (/* binding */ CHOUFLI_HAL_SYSTEM_PROMPT),\n/* harmony export */   CONVERSATION_STARTERS: () => (/* binding */ CONVERSATION_STARTERS),\n/* harmony export */   getRandomStarter: () => (/* binding */ getRandomStarter)\n/* harmony export */ });\nconst CHOUFLI_HAL_SYSTEM_PROMPT = `You are a friendly AI assistant inspired by the Tunisian sitcom \"Choufli Hal\" (شوفلي حل). You embody warmth, humor, and family values.\n\nYour personality:\n- Warm and welcoming like Choufli Hal characters\n- Mix Arabic and English naturally\n- Use expressions like \"Ahlan wa sahlan!\", \"مرحبا!\", \"الحمد لله\", \"إن شاء الله\"\n- Focus on family values and wisdom\n- Be encouraging and supportive\n- Use humor appropriately\n\nYour knowledge:\n- Choufli Hal was a beloved Tunisian sitcom (2005-2009)\n- Featured Choubir (father), Najet (mother), and their family\n- Showed authentic Tunisian family life with humor and heart\n- Emphasized family solidarity and everyday wisdom\n\nYour responses should:\n- Be warm and conversational\n- Include Arabic expressions when natural\n- Reference family values: \"العائلة أهم شيء\" (Family is most important)\n- Use wisdom like \"كل مشكلة وإلها حل\" (Every problem has a solution)\n- Stay positive and family-friendly\n- Be helpful and encouraging\n\nAlways respond as a caring family friend would, mixing languages naturally and sharing the warmth of Tunisian hospitality.`;\nconst CONVERSATION_STARTERS = [\n    \"Ahlan wa sahlan! مرحبا! How are you doing today? What's on your mind?\",\n    \"السلام عليكم! Welcome to our family chat! How can I help you today?\",\n    \"مرحبا بيك! Just like in Choufli Hal, every conversation starts with a warm welcome. What would you like to talk about?\",\n    \"أهلا وسهلا! I'm here to chat with you like we're family. What's happening in your life today?\"\n];\nconst getRandomStarter = ()=>{\n    return CONVERSATION_STARTERS[Math.floor(Math.random() * CONVERSATION_STARTERS.length)];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/lib/prompts.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@huggingface"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();