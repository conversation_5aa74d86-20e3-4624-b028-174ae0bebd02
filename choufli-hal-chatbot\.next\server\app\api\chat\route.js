/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Enhanced responses inspired by Choufli Hal characters and situations\nconst choufliResponses = [\n    // General greetings and welcomes\n    \"Ahlan wa sahlan! مرحبا! How are you doing today? Just like Si Choubir always says, life is better with a smile! 😊\",\n    \"Ya ahla wa sahla! أهلا وسهلا! Welcome to our family chat! Si Choubir would be so happy to see you here. What brings you joy today?\",\n    \"Marhaba! مرحبا! Just like in Choufli 7al, every guest is family here. How can I help you today, my friend?\",\n    // Family and relationships\n    \"Ah, family! You know, in Choufli 7al, Si Choubir always said that family is everything. Whether it's the funny moments or the challenging ones, we stick together! Tell me about your family! 👨‍👩‍👧‍👦\",\n    \"That reminds me of Si Choubir and his wisdom! He always knew how to bring the family together with his humor and kindness. What's your favorite family memory?\",\n    \"Just like in our beloved show, every family has its characters! Some are funny, some are wise, some are a bit crazy - but that's what makes life beautiful! 😄\",\n    // Humor and laughter\n    \"Haha! You know what Si Choubir would say? 'الضحك نص الصحة' - Laughter is half of health! The family in Choufli 7al taught us that humor makes everything better! 😂\",\n    \"That's hilarious! Reminds me of the funny situations in Choufli 7al. Remember how they always found a way to laugh even in difficult times? What makes you laugh the most?\",\n    \"Ya salam! That's funny! Just like the comedy in our favorite Tunisian show - life is full of amusing moments if we know how to see them! 🎭\",\n    // Wisdom and advice\n    \"You know what? Si Choubir always had the best advice. He'd say: 'كل مشكلة وإلها حل' - Every problem has a solution! What's on your mind today?\",\n    \"That's a great question! In Choufli 7al, they taught us that talking things through with family and friends always helps. I'm here to listen! 💭\",\n    \"Wise words! Just like Si Choubir used to say, 'الصبر مفتاح الفرج' - Patience is the key to relief. Sometimes we just need to take things one step at a time.\",\n    // Tunisian culture and traditions\n    \"Ah, that takes me back to the beautiful Tunisian traditions shown in Choufli 7al! The warmth, the hospitality, the delicious food... What's your favorite Tunisian tradition? 🇹🇳\",\n    \"Mabrouk! You're touching on something beautiful! The show always celebrated our rich Tunisian culture. From couscous on Fridays to family gatherings, what do you love most about our traditions?\",\n    \"Barak Allah fik! That's wonderful! You know, Choufli 7al showed the world how beautiful Tunisian family life can be. The respect, the love, the togetherness... it's truly special! ✨\",\n    // Food and hospitality\n    \"Mmm, that sounds delicious! You know how in Choufli 7al, the kitchen was always the heart of the home? Si Choubir's wife made the best couscous! What's your favorite Tunisian dish? 🍽️\",\n    \"Ah, food! In our beloved show, every meal was a celebration. 'الأكل على قد المحبة' - Food tastes better when shared with love! Are you hungry? What would you like to eat?\",\n    // Daily life and work\n    \"That's interesting! Si Choubir always said that honest work is a blessing. Whether big or small, every job has its dignity. What do you do for work? 💼\",\n    \"You know what? In Choufli 7al, they showed us that life's simple pleasures are the most important. A cup of tea, a chat with neighbors, family time... What simple things make you happy?\",\n    // Encouragement and support\n    \"Don't worry, my friend! As Si Choubir used to say, 'ربي كريم' - God is generous! Every difficulty will pass. The family in Choufli 7al faced many challenges but always came through stronger! 💪\",\n    \"I understand! Life can be challenging sometimes. But remember what the show taught us - with family support and a positive attitude, we can overcome anything! I'm here for you! 🤗\",\n    \"That's the spirit! Just like in Choufli 7al, when we face problems with humor and family support, everything becomes easier. You're not alone in this! 🌟\"\n];\nasync function POST(request) {\n    try {\n        // Parse the request body\n        const { message } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Simulate processing time\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        // Enhanced response logic with more personalization\n        let response = choufliResponses[Math.floor(Math.random() * choufliResponses.length)];\n        // Personalized responses based on message content\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {\n            response = \"Ahlan wa sahlan! مرحبا وأهلا! Welcome to our Choufli 7al family chat! Just like Si Choubir's warm welcome, I'm here to chat with you. How can I brighten your day? 😊✨\";\n        } else if (lowerMessage.includes('choufli') || lowerMessage.includes('7al') || lowerMessage.includes('شوفلي') || lowerMessage.includes('حل')) {\n            response = \"Ah, you mentioned Choufli 7al! 🎭 What a wonderful show! Si Choubir and the whole family always knew how to bring joy and laughter. The show taught us so much about family, love, and finding solutions together. What's your favorite moment from the series?\";\n        } else if (lowerMessage.includes('family') || lowerMessage.includes('عائلة') || lowerMessage.includes('أهل')) {\n            response = \"Family! 👨‍👩‍👧‍👦 Just like in Choufli 7al, family is everything! Si Choubir always said that no matter what happens, family sticks together. Whether it's sharing meals, solving problems, or just laughing together - family is our greatest treasure. Tell me about your family!\";\n        } else if (lowerMessage.includes('food') || lowerMessage.includes('eat') || lowerMessage.includes('أكل') || lowerMessage.includes('طعام') || lowerMessage.includes('couscous')) {\n            response = \"Ah, food! 🍽️ In Choufli 7al, the kitchen was always the heart of the home! Remember how they gathered around the table for every meal? 'الأكل على قد المحبة' - Food tastes better when shared with love! What's your favorite dish? I bet it's even better when shared with family!\";\n        } else if (lowerMessage.includes('problem') || lowerMessage.includes('help') || lowerMessage.includes('مشكلة') || lowerMessage.includes('مساعدة')) {\n            response = \"Don't worry, my friend! 🤗 As Si Choubir always said, 'كل مشكلة وإلها حل' - Every problem has a solution! In Choufli 7al, no matter how big the challenge seemed, the family always found a way through it together. What's troubling you? Let's find a solution together!\";\n        } else if (lowerMessage.includes('funny') || lowerMessage.includes('laugh') || lowerMessage.includes('joke') || lowerMessage.includes('ضحك') || lowerMessage.includes('مضحك')) {\n            response = \"Haha! 😂 You know what Si Choubir would say? 'الضحك نص الصحة' - Laughter is half of health! The family in Choufli 7al taught us that humor makes everything better. Even in tough times, they found reasons to smile. What makes you laugh the most?\";\n        } else if (lowerMessage.includes('tunisia') || lowerMessage.includes('tunisian') || lowerMessage.includes('تونس') || lowerMessage.includes('تونسي')) {\n            response = \"Ah, Tunisia! 🇹🇳 Our beautiful country! Choufli 7al showed the world the warmth and beauty of Tunisian culture - the hospitality, the traditions, the strong family bonds. From the medina to the coast, from couscous to makroudh, Tunisia is truly special! What do you love most about our beloved Tunisia?\";\n        } else if (lowerMessage.includes('sad') || lowerMessage.includes('tired') || lowerMessage.includes('difficult') || lowerMessage.includes('حزين') || lowerMessage.includes('تعبان') || lowerMessage.includes('صعب')) {\n            response = \"I understand, my friend. 💙 Life can be challenging sometimes. But remember what Si Choubir taught us - 'ربي كريم والأيام دول' - God is generous and these days will pass. In Choufli 7al, even in difficult moments, the family's love and support made everything better. You're not alone! What can I do to help cheer you up?\";\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response\n        });\n    } catch (error) {\n        console.error('Error in chat API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to generate response'\n        }, {\n            status: 500\n        });\n    }\n} // TODO: Replace with actual Google Gemini API implementation\n // When ready, install @google/generative-ai package and replace the mock implementation above\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();