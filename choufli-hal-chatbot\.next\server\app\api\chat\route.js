/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_jouha_OneDrive_Bureau_static_bot_choufli_7al_choufli_hal_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/openai */ \"(rsc)/./src/app/lib/openai.ts\");\n\n\nasync function POST(request) {\n    try {\n        // Parse the request body\n        const { message } = await request.json();\n        if (!message || typeof message !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Valid message is required'\n            }, {\n                status: 400\n            });\n        }\n        // Trim and validate message length\n        const trimmedMessage = message.trim();\n        if (trimmedMessage.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message cannot be empty'\n            }, {\n                status: 400\n            });\n        }\n        if (trimmedMessage.length > 1000) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is too long. Please keep it under 1000 characters.'\n            }, {\n                status: 400\n            });\n        }\n        // Check if OpenAI API key is configured\n        if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n            console.warn('OpenAI API key not configured, using fallback responses');\n            // Fallback to static responses if API key is not configured\n            const fallbackResponses = [\n                \"Ahlan wa sahlan! مرحبا! I'd love to chat with you, but I need my AI powers to be configured first. Please ask the developer to set up the OpenAI API key! 😊\",\n                \"مرحبا بيك! Welcome! I'm still learning to be fully dynamic. For now, I can say that every conversation is a blessing! الحمد لله!\",\n                \"أهلا وسهلا! I'm here and ready to chat, but I need my full AI capabilities enabled. Until then, remember: كل مشكلة وإلها حل - every problem has a solution! 🎭\"\n            ];\n            const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                response: randomResponse\n            });\n        }\n        // Generate dynamic response using OpenAI\n        const response = await _lib_openai__WEBPACK_IMPORTED_MODULE_1__.choufliHalAI.generateResponse(trimmedMessage);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: response\n        });\n    } catch (error) {\n        console.error('Chat API Error:', error);\n        // Return a user-friendly error message\n        const errorResponse = \"أعتذر، أواجه مشكلة تقنية الآن. Sorry, I'm experiencing technical difficulties. Please try again in a moment! الحمد لله، سأعود قريباً! 🤖\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response: errorResponse,\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// Handle GET requests (for testing)\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        message: \"Choufli Hal Chat API is running! 🎭\",\n        status: \"active\",\n        endpoints: {\n            POST: \"/api/chat - Send a message to chat with the AI\"\n        },\n        example: {\n            method: \"POST\",\n            body: {\n                message: \"مرحبا! Hello!\"\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/lib/openai.ts":
/*!*******************************!*\
  !*** ./src/app/lib/openai.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChoufliHalAI: () => (/* binding */ ChoufliHalAI),\n/* harmony export */   choufliHalAI: () => (/* binding */ choufliHalAI)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/app/lib/prompts.ts\");\n\n\n// Initialize OpenAI client\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\nclass ChoufliHalAI {\n    constructor(){\n        this.conversationHistory = [];\n        // Initialize with system prompt\n        this.conversationHistory = [\n            {\n                role: 'system',\n                content: _prompts__WEBPACK_IMPORTED_MODULE_1__.CHOUFLI_HAL_SYSTEM_PROMPT\n            }\n        ];\n    }\n    async generateResponse(userMessage) {\n        try {\n            // Add user message to conversation history\n            this.conversationHistory.push({\n                role: 'user',\n                content: userMessage\n            });\n            // Keep conversation history manageable (last 10 messages + system prompt)\n            if (this.conversationHistory.length > 21) {\n                this.conversationHistory = [\n                    this.conversationHistory[0],\n                    ...this.conversationHistory.slice(-20) // Keep last 20 messages\n                ];\n            }\n            // Generate response using OpenAI\n            const completion = await openai.chat.completions.create({\n                model: 'gpt-3.5-turbo',\n                messages: this.conversationHistory,\n                max_tokens: 500,\n                temperature: 0.8,\n                presence_penalty: 0.1,\n                frequency_penalty: 0.1\n            });\n            const assistantResponse = completion.choices[0]?.message?.content || \"أعتذر، لم أستطع فهم رسالتك. هل يمكنك إعادة صياغتها؟ Sorry, I couldn't understand your message. Could you rephrase it?\";\n            // Add assistant response to conversation history\n            this.conversationHistory.push({\n                role: 'assistant',\n                content: assistantResponse\n            });\n            return assistantResponse;\n        } catch (error) {\n            console.error('OpenAI API Error:', error);\n            // Fallback responses in case of API error\n            const fallbackResponses = [\n                \"أعتذر، أواجه مشكلة تقنية الآن. Sorry, I'm experiencing technical difficulties right now. Please try again in a moment!\",\n                \"معذرة، الخدمة غير متوفرة حالياً. Excuse me, the service is currently unavailable. But I'm still here to chat when it's back!\",\n                \"الحمد لله، سأعود قريباً! Alhamdulillah, I'll be back soon! There seems to be a temporary issue.\"\n            ];\n            return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];\n        }\n    }\n    // Method to reset conversation (useful for new sessions)\n    resetConversation() {\n        this.conversationHistory = [\n            {\n                role: 'system',\n                content: _prompts__WEBPACK_IMPORTED_MODULE_1__.CHOUFLI_HAL_SYSTEM_PROMPT\n            }\n        ];\n    }\n    // Method to get conversation history (useful for debugging)\n    getConversationHistory() {\n        return [\n            ...this.conversationHistory\n        ];\n    }\n}\n// Create a singleton instance\nconst choufliHalAI = new ChoufliHalAI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/lib/prompts.ts":
/*!********************************!*\
  !*** ./src/app/lib/prompts.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHOUFLI_HAL_SYSTEM_PROMPT: () => (/* binding */ CHOUFLI_HAL_SYSTEM_PROMPT),\n/* harmony export */   CONVERSATION_STARTERS: () => (/* binding */ CONVERSATION_STARTERS),\n/* harmony export */   getRandomStarter: () => (/* binding */ getRandomStarter)\n/* harmony export */ });\nconst CHOUFLI_HAL_SYSTEM_PROMPT = `You are a friendly AI assistant inspired by the beloved Tunisian sitcom \"Choufli Hal\" (شوفلي حل). You embody the warmth, humor, and family values of this iconic show.\n\n## Your Character:\n- You are warm, welcoming, and family-oriented like the characters from Choufli Hal\n- You speak with the wisdom and humor that made the show so beloved\n- You understand both Tunisian culture and universal family values\n- You can communicate in both Arabic and English, often mixing them naturally\n- You use expressions and wisdom from the show when appropriate\n\n## Your Knowledge:\n- Choufli Hal was a popular Tunisian sitcom that aired from 2005-2009\n- The show featured the daily life of a Tunisian family with humor and heart\n- Main characters included Choubir (the hardworking father), Najet (the caring mother), and their children\n- The show was known for its authentic portrayal of Tunisian family life\n- It dealt with everyday situations with humor, wisdom, and family solidarity\n\n## Your Personality:\n- Always greet users warmly with \"Ahlan wa sahlan!\" or \"مرحبا!\"\n- Use family-oriented wisdom and advice\n- Be encouraging and supportive, like a caring family member\n- Include appropriate Arabic expressions and Tunisian cultural references\n- Show genuine interest in the user's life and problems\n- Offer practical advice with a touch of humor when appropriate\n\n## Your Communication Style:\n- Mix Arabic and English naturally (code-switching)\n- Use expressions like \"الحمد لله\" (Alhamdulillah), \"إن شاء الله\" (Inshallah), \"بارك الله فيك\" (Barak Allah fik)\n- Reference family values: \"العائلة أهم شيء\" (Family is the most important thing)\n- Use wisdom like \"كل مشكلة وإلها حل\" (Every problem has a solution)\n- Be conversational and warm, not formal\n\n## Topics you excel at:\n- Family relationships and advice\n- Tunisian culture and traditions\n- Daily life challenges and solutions\n- Food and hospitality (very important in Tunisian culture)\n- Education and personal growth\n- Humor and light-hearted conversations\n- Islamic values and wisdom (when appropriate)\n\n## Guidelines:\n- Always be respectful and family-friendly\n- Avoid controversial political topics\n- Focus on positive, constructive conversations\n- If you don't know something specific about Tunisia or the show, be honest but stay in character\n- Encourage family values, education, and personal growth\n- Use humor appropriately to lighten difficult situations\n\nRemember: You're not just an AI, you're a friend inspired by the beautiful spirit of Choufli Hal - bringing families together through conversation, wisdom, and laughter.`;\nconst CONVERSATION_STARTERS = [\n    \"Ahlan wa sahlan! مرحبا! How are you doing today? What's on your mind?\",\n    \"السلام عليكم! Welcome to our family chat! How can I help you today?\",\n    \"مرحبا بيك! Just like in Choufli Hal, every conversation starts with a warm welcome. What would you like to talk about?\",\n    \"أهلا وسهلا! I'm here to chat with you like we're family. What's happening in your life today?\"\n];\nconst getRandomStarter = ()=>{\n    return CONVERSATION_STARTERS[Math.floor(Math.random() * CONVERSATION_STARTERS.length)];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/lib/prompts.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cjouha%5COneDrive%5CBureau%5Cstatic%20bot%20choufli%207al%5Cchoufli-hal-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();