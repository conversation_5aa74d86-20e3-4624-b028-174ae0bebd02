{"$id": "/inference/schemas/visual-question-answering/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Visual Question Answering inference", "title": "VisualQuestionAnsweringInput", "type": "object", "properties": {"inputs": {"description": "One (image, question) pair to answer", "type": "object", "title": "VisualQuestionAnsweringInputData", "properties": {"image": {"description": "The image.", "comment": "type=binary"}, "question": {"description": "The question to answer based on the image.", "type": "string"}}, "required": ["question", "image"]}, "parameters": {"description": "Additional inference parameters for Visual Question Answering", "$ref": "#/$defs/VisualQuestionAnsweringParameters"}}, "$defs": {"VisualQuestionAnsweringParameters": {"title": "VisualQuestionAnsweringParameters", "type": "object", "properties": {"top_k": {"type": "integer", "description": "The number of answers to return (will be chosen by order of likelihood). Note that we return less than topk answers if there are not enough options available within the context."}}}}, "required": ["inputs"]}