/**
 * Inference code generated from the JSON schema spec in ./spec
 *
 * Using src/scripts/inference-codegen
 */
/**
 * Inputs for Depth Estimation inference
 */
export interface DepthEstimationInput {
    /**
     * The input image data
     */
    inputs: unknown;
    /**
     * Additional inference parameters for Depth Estimation
     */
    parameters?: {
        [key: string]: unknown;
    };
    [property: string]: unknown;
}
/**
 * Outputs of inference for the Depth Estimation task
 */
export interface DepthEstimationOutput {
    /**
     * The predicted depth as an image
     */
    depth?: unknown;
    /**
     * The predicted depth as a tensor
     */
    predicted_depth?: unknown;
    [property: string]: unknown;
}
//# sourceMappingURL=inference.d.ts.map