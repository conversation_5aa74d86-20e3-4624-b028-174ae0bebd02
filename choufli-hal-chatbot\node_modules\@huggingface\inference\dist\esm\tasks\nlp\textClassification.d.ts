import type { TextClassificationInput, TextClassificationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type TextClassificationArgs = BaseArgs & TextClassificationInput;
/**
 * Usually used for sentiment-analysis this will output the likelihood of classes of an input. Recommended model: distilbert-base-uncased-finetuned-sst-2-english
 */
export declare function textClassification(args: TextClassificationArgs, options?: Options): Promise<TextClassificationOutput>;
//# sourceMappingURL=textClassification.d.ts.map