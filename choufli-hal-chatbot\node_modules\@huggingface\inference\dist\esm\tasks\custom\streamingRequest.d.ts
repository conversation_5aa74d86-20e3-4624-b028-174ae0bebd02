import type { InferenceTask, Options, RequestArgs } from "../../types.js";
/**
 * Primitive to make custom inference calls that expect server-sent events, and returns the response through a generator
 * @deprecated Use specific task functions instead. This function will be removed in a future version.
 */
export declare function streamingRequest<T>(args: RequestArgs, options?: Options & {
    /** In most cases (unless we pass a endpointUrl) we know the task */
    task?: InferenceTask;
}): AsyncGenerator<T>;
//# sourceMappingURL=streamingRequest.d.ts.map