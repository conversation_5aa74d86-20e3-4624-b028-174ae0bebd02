{"$id": "/inference/schemas/text-to-speech/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Text To Speech inference", "title": "TextToSpeechInput", "type": "object", "properties": {"inputs": {"description": "The input text data", "type": "string"}, "parameters": {"description": "Additional inference parameters for Text To Speech", "$ref": "#/$defs/TextToSpeechParameters"}}, "$defs": {"TextToSpeechParameters": {"title": "TextToSpeechParameters", "type": "object", "properties": {"generation_parameters": {"description": "Parametrization of the text generation process", "$ref": "/inference/schemas/common-definitions.json#/definitions/GenerationParameters"}}}}, "required": ["inputs"]}