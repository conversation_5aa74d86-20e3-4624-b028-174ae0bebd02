import { NextRequest, NextResponse } from 'next/server';
import { choufliHalAI } from '../../lib/huggingface-simple';

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { message } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Valid message is required' },
        { status: 400 }
      );
    }

    // Trim and validate message length
    const trimmedMessage = message.trim();
    if (trimmedMessage.length === 0) {
      return NextResponse.json(
        { error: 'Message cannot be empty' },
        { status: 400 }
      );
    }

    if (trimmedMessage.length > 1000) {
      return NextResponse.json(
        { error: 'Message is too long. Please keep it under 1000 characters.' },
        { status: 400 }
      );
    }

    // Check if Hugging Face API key is configured
    if (!process.env.HUGGINGFACE_API_KEY || process.env.HUGGINGFACE_API_KEY === 'your_huggingface_api_key_here') {
      console.warn('Hugging Face API key not configured, using fallback responses');

      // Fallback to static responses if API key is not configured
      const fallbackResponses = [
        "Ahlan wa sahlan! مرحبا! I'd love to chat with you, but I need my Hugging Face API key to be configured first. It's FREE to get one! 😊",
        "مرحبا بيك! Welcome! I'm still learning to be fully dynamic. For now, I can say that every conversation is a blessing! الحمد لله!",
        "أهلا وسهلا! I'm here and ready to chat, but I need my full AI capabilities enabled. Get a FREE Hugging Face API key to unlock my potential! 🎭",
        "Just like in Choufli Hal, 'كل مشكلة وإلها حل' - every problem has a solution! Get your free Hugging Face API key to chat with me properly! 🤖"
      ];

      const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];

      return NextResponse.json({
        response: randomResponse
      });
    }

    // Generate dynamic response using Hugging Face
    const response = await choufliHalAI.generateResponse(trimmedMessage);

    return NextResponse.json({
      response: response
    });

  } catch (error) {
    console.error('Chat API Error:', error);
    
    // Return a user-friendly error message
    const errorResponse = "أعتذر، أواجه مشكلة تقنية الآن. Sorry, I'm experiencing technical difficulties. Please try again in a moment! الحمد لله، سأعود قريباً! 🤖";
    
    return NextResponse.json(
      { 
        response: errorResponse,
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// Handle GET requests (for testing)
export async function GET() {
  return NextResponse.json({
    message: "Choufli Hal Chat API is running! 🎭",
    status: "active",
    aiProvider: "Hugging Face (FREE)",
    endpoints: {
      POST: "/api/chat - Send a message to chat with the AI"
    },
    example: {
      method: "POST",
      body: {
        message: "مرحبا! Hello!"
      }
    },
    setup: {
      apiKey: "Get your FREE Hugging Face API key from: https://huggingface.co/settings/tokens",
      envVar: "HUGGINGFACE_API_KEY"
    }
  });
}
