import { NextRequest, NextResponse } from 'next/server';
import { choufliHalAI } from '../../lib/openai';

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { message } = await request.json();

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Valid message is required' },
        { status: 400 }
      );
    }

    // Trim and validate message length
    const trimmedMessage = message.trim();
    if (trimmedMessage.length === 0) {
      return NextResponse.json(
        { error: 'Message cannot be empty' },
        { status: 400 }
      );
    }

    if (trimmedMessage.length > 1000) {
      return NextResponse.json(
        { error: 'Message is too long. Please keep it under 1000 characters.' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      console.warn('OpenAI API key not configured, using fallback responses');
      
      // Fallback to static responses if API key is not configured
      const fallbackResponses = [
        "Ah<PERSON> wa sahlan! مرحبا! I'd love to chat with you, but I need my AI powers to be configured first. Please ask the developer to set up the OpenAI API key! 😊",
        "مرحبا بيك! Welcome! I'm still learning to be fully dynamic. For now, I can say that every conversation is a blessing! الحمد لله!",
        "أهلا وسهلا! I'm here and ready to chat, but I need my full AI capabilities enabled. Until then, remember: كل مشكلة وإلها حل - every problem has a solution! 🎭"
      ];
      
      const randomResponse = fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
      
      return NextResponse.json({
        response: randomResponse
      });
    }

    // Generate dynamic response using OpenAI
    const response = await choufliHalAI.generateResponse(trimmedMessage);

    return NextResponse.json({
      response: response
    });

  } catch (error) {
    console.error('Chat API Error:', error);
    
    // Return a user-friendly error message
    const errorResponse = "أعتذر، أواجه مشكلة تقنية الآن. Sorry, I'm experiencing technical difficulties. Please try again in a moment! الحمد لله، سأعود قريباً! 🤖";
    
    return NextResponse.json(
      { 
        response: errorResponse,
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

// Handle GET requests (for testing)
export async function GET() {
  return NextResponse.json({
    message: "Choufli Hal Chat API is running! 🎭",
    status: "active",
    endpoints: {
      POST: "/api/chat - Send a message to chat with the AI"
    },
    example: {
      method: "POST",
      body: {
        message: "مرحبا! Hello!"
      }
    }
  });
}
