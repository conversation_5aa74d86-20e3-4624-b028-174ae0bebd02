import { NextRequest, NextResponse } from 'next/server';

// Mock responses for demonstration - replace with actual Gemini API when package is working
const mockResponses = [
  "Ahlan wa sahlan! مرحبا! How are you doing today? Just like <PERSON> always says, life is better with a smile! 😊",
  "<PERSON><PERSON>, that reminds me of something from Choufli 7al! You know how the family always finds a way to laugh together? What's making you happy today?",
  "Ya salam! That's interesting! In the spirit of our beloved Tunisian sitcom, let me tell you - every problem has a solution, just like in the show! 🎭",
  "<PERSON><PERSON><PERSON>! You're asking great questions! <PERSON> would be proud. The family always taught us to be curious and kind. What else would you like to chat about?",
  "Barak Allah fik! That's a wonderful topic! You know, in Choufli 7al, they always showed us that family and friendship are the most important things. Tell me more!",
];

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { message } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Get a random mock response
    const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)];
    
    // Add some personalization based on the message
    let response = randomResponse;
    if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      response = "Ahlan wa sahlan! مرحبا! Welcome to our Choufli 7al chat! Just like the warm family in the show, I'm here to chat with you. How can I help you today? 😊";
    } else if (message.toLowerCase().includes('choufli') || message.toLowerCase().includes('7al')) {
      response = "Ah, you mentioned Choufli 7al! 🎭 What a wonderful show! Si Choubir and the whole family always knew how to bring joy and laughter. What's your favorite moment from the show?";
    }

    return NextResponse.json({ response });

  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Failed to generate response' },
      { status: 500 }
    );
  }
}

// TODO: Replace with actual Google Gemini API implementation
// When ready, install @google/generative-ai package and replace the mock implementation above
