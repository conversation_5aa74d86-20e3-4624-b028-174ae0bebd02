import { NextRequest, NextResponse } from 'next/server';

// Enhanced responses inspired by Choufli Hal characters and situations
const choufliResponses = [
  // General greetings and welcomes
  "<PERSON><PERSON> wa sahlan! مرحبا! How are you doing today? Just like <PERSON> always says, life is better with a smile! 😊",
  "Ya ahla wa sahla! أهلا وسهلا! Welcome to our family chat! <PERSON> would be so happy to see you here. What brings you joy today?",
  "Marhaba! مرحبا! Just like in Choufli 7al, every guest is family here. How can I help you today, my friend?",

  // Family and relationships
  "Ah, family! You know, in Choufli 7al, <PERSON> always said that family is everything. Whether it's the funny moments or the challenging ones, we stick together! Tell me about your family! 👨‍👩‍👧‍👦",
  "That reminds me of <PERSON> and his wisdom! He always knew how to bring the family together with his humor and kindness. What's your favorite family memory?",
  "Just like in our beloved show, every family has its characters! Some are funny, some are wise, some are a bit crazy - but that's what makes life beautiful! 😄",

  // Humor and laughter
  "Haha! You know what <PERSON> would say? 'الضحك نص الصحة' - Laughter is half of health! The family in Choufli 7al taught us that humor makes everything better! 😂",
  "That's hilarious! Reminds me of the funny situations in <PERSON>ufli 7al. Remember how they always found a way to laugh even in difficult times? What makes you laugh the most?",
  "Ya salam! That's funny! Just like the comedy in our favorite Tunisian show - life is full of amusing moments if we know how to see them! 🎭",

  // Wisdom and advice
  "You know what? <PERSON> <PERSON><PERSON>r always had the best advice. He'd say: 'كل مشكلة وإلها حل' - Every problem has a solution! What's on your mind today?",
  "That's a great question! In Choufli 7al, they taught us that talking things through with family and friends always helps. I'm here to listen! 💭",
  "Wise words! Just like Si Choubir used to say, 'الصبر مفتاح الفرج' - Patience is the key to relief. Sometimes we just need to take things one step at a time.",

  // Tunisian culture and traditions
  "Ah, that takes me back to the beautiful Tunisian traditions shown in Choufli 7al! The warmth, the hospitality, the delicious food... What's your favorite Tunisian tradition? 🇹🇳",
  "Mabrouk! You're touching on something beautiful! The show always celebrated our rich Tunisian culture. From couscous on Fridays to family gatherings, what do you love most about our traditions?",
  "Barak Allah fik! That's wonderful! You know, Choufli 7al showed the world how beautiful Tunisian family life can be. The respect, the love, the togetherness... it's truly special! ✨",

  // Food and hospitality
  "Mmm, that sounds delicious! You know how in Choufli 7al, the kitchen was always the heart of the home? Si Choubir's wife made the best couscous! What's your favorite Tunisian dish? 🍽️",
  "Ah, food! In our beloved show, every meal was a celebration. 'الأكل على قد المحبة' - Food tastes better when shared with love! Are you hungry? What would you like to eat?",

  // Daily life and work
  "That's interesting! Si Choubir always said that honest work is a blessing. Whether big or small, every job has its dignity. What do you do for work? 💼",
  "You know what? In Choufli 7al, they showed us that life's simple pleasures are the most important. A cup of tea, a chat with neighbors, family time... What simple things make you happy?",

  // Encouragement and support
  "Don't worry, my friend! As Si Choubir used to say, 'ربي كريم' - God is generous! Every difficulty will pass. The family in Choufli 7al faced many challenges but always came through stronger! 💪",
  "I understand! Life can be challenging sometimes. But remember what the show taught us - with family support and a positive attitude, we can overcome anything! I'm here for you! 🤗",
  "That's the spirit! Just like in Choufli 7al, when we face problems with humor and family support, everything becomes easier. You're not alone in this! 🌟"
];

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const { message } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Enhanced response logic with more personalization
    let response = choufliResponses[Math.floor(Math.random() * choufliResponses.length)];

    // Personalized responses based on message content
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {
      response = "Ahlan wa sahlan! مرحبا وأهلا! Welcome to our Choufli 7al family chat! Just like Si Choubir's warm welcome, I'm here to chat with you. How can I brighten your day? 😊✨";
    }
    else if (lowerMessage.includes('choufli') || lowerMessage.includes('7al') || lowerMessage.includes('شوفلي') || lowerMessage.includes('حل')) {
      response = "Ah, you mentioned Choufli 7al! 🎭 What a wonderful show! Si Choubir and the whole family always knew how to bring joy and laughter. The show taught us so much about family, love, and finding solutions together. What's your favorite moment from the series?";
    }
    else if (lowerMessage.includes('family') || lowerMessage.includes('عائلة') || lowerMessage.includes('أهل')) {
      response = "Family! 👨‍👩‍👧‍👦 Just like in Choufli 7al, family is everything! Si Choubir always said that no matter what happens, family sticks together. Whether it's sharing meals, solving problems, or just laughing together - family is our greatest treasure. Tell me about your family!";
    }
    else if (lowerMessage.includes('food') || lowerMessage.includes('eat') || lowerMessage.includes('أكل') || lowerMessage.includes('طعام') || lowerMessage.includes('couscous')) {
      response = "Ah, food! 🍽️ In Choufli 7al, the kitchen was always the heart of the home! Remember how they gathered around the table for every meal? 'الأكل على قد المحبة' - Food tastes better when shared with love! What's your favorite dish? I bet it's even better when shared with family!";
    }
    else if (lowerMessage.includes('problem') || lowerMessage.includes('help') || lowerMessage.includes('مشكلة') || lowerMessage.includes('مساعدة')) {
      response = "Don't worry, my friend! 🤗 As Si Choubir always said, 'كل مشكلة وإلها حل' - Every problem has a solution! In Choufli 7al, no matter how big the challenge seemed, the family always found a way through it together. What's troubling you? Let's find a solution together!";
    }
    else if (lowerMessage.includes('funny') || lowerMessage.includes('laugh') || lowerMessage.includes('joke') || lowerMessage.includes('ضحك') || lowerMessage.includes('مضحك')) {
      response = "Haha! 😂 You know what Si Choubir would say? 'الضحك نص الصحة' - Laughter is half of health! The family in Choufli 7al taught us that humor makes everything better. Even in tough times, they found reasons to smile. What makes you laugh the most?";
    }
    else if (lowerMessage.includes('tunisia') || lowerMessage.includes('tunisian') || lowerMessage.includes('تونس') || lowerMessage.includes('تونسي')) {
      response = "Ah, Tunisia! 🇹🇳 Our beautiful country! Choufli 7al showed the world the warmth and beauty of Tunisian culture - the hospitality, the traditions, the strong family bonds. From the medina to the coast, from couscous to makroudh, Tunisia is truly special! What do you love most about our beloved Tunisia?";
    }
    else if (lowerMessage.includes('sad') || lowerMessage.includes('tired') || lowerMessage.includes('difficult') || lowerMessage.includes('حزين') || lowerMessage.includes('تعبان') || lowerMessage.includes('صعب')) {
      response = "I understand, my friend. 💙 Life can be challenging sometimes. But remember what Si Choubir taught us - 'ربي كريم والأيام دول' - God is generous and these days will pass. In Choufli 7al, even in difficult moments, the family's love and support made everything better. You're not alone! What can I do to help cheer you up?";
    }

    return NextResponse.json({ response });

  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { error: 'Failed to generate response' },
      { status: 500 }
    );
  }
}

// TODO: Replace with actual Google Gemini API implementation
// When ready, install @google/generative-ai package and replace the mock implementation above
