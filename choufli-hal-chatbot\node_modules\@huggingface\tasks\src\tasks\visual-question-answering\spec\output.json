{"$id": "/inference/schemas/visual-question-answering/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Visual Question Answering task", "title": "VisualQuestionAnsweringOutput", "type": "array", "items": {"type": "object", "properties": {"answer": {"type": "string", "description": "The answer to the question"}, "score": {"type": "number", "description": "The associated score / probability"}}, "required": ["score"]}}