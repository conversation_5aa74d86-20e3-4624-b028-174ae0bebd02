import type { QuestionAnsweringInput, QuestionAnsweringOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type QuestionAnsweringArgs = BaseArgs & QuestionAnsweringInput;
/**
 * Want to have a nice know-it-all bot that can answer any question?. Recommended model: deepset/roberta-base-squad2
 */
export declare function questionAnswering(args: QuestionAnsweringArgs, options?: Options): Promise<QuestionAnsweringOutput[number]>;
//# sourceMappingURL=questionAnswering.d.ts.map