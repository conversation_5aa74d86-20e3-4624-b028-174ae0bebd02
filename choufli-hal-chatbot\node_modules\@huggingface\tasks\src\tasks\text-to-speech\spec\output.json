{"$id": "/inference/schemas/text-to-speech/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Text To Speech task", "title": "TextToSpeechOutput", "type": "object", "properties": {"audio": {"description": "The generated audio", "comment": "type=binary"}, "sampling_rate": {"type": "number", "description": "The sampling rate of the generated audio waveform."}}, "required": ["audio"]}