## About the Task

Unconditional image generation is the task of generating new images without any specific input. The main goal of this is to create novel, original images that are not based on existing images.
This can be used for a variety of applications, such as creating new artistic images, improving image recognition algorithms, or generating photorealistic images for virtual reality environments.

Unconditional image generation models usually start with a _seed_ that generates a _random noise vector_. The model will then use this vector to create an output image similar to the images used for training the model.

An example of unconditional image generation would be generating the image of a face on a model trained with the [CelebA dataset](https://huggingface.co/datasets/huggan/CelebA-HQ) or [generating a butterfly](https://huggingface.co/spaces/huggan/butterfly-gan) on a model trained with the [Smithsonian Butterflies dataset](https://huggingface.co/datasets/ceyda/smithsonian_butterflies).

[Generative adversarial networks](https://en.wikipedia.org/wiki/Generative_adversarial_network) and [Diffusion](https://huggingface.co/docs/diffusers/index) are common architectures for this task.

## Use Cases

Unconditional image generation can be used for a variety of applications.

### Artistic Expression

Unconditional image generation can be used to create novel, original artwork that is not based on any existing images. This can be used to explore new creative possibilities and produce unique, imaginative images.

### Data Augmentation

Unconditional image generation models can be used to generate new images to improve the performance of image recognition algorithms. This makes algorithms more robust and able to handle a broader range of images.

### Virtual Reality

Unconditional image generation models can be used to create photorealistic images that can be used in virtual reality environments. This makes the VR experience more immersive and realistic.

### Medical Imaging

Unconditional image generation models can generate new medical images, such as CT or MRI scans, that can be used to train and evaluate medical imaging algorithms. This can improve the accuracy and reliability of these algorithms.

### Industrial Design

Unconditional image generation models can generate new designs for products, such as clothing or furniture, that are not based on any existing designs. This way, designers can explore new creative possibilities and produce unique, innovative designs.

## Model Hosting and Inference

This section should have useful information about Model Hosting and Inference

## Useful Resources

- [Hugging Face Diffusion Models Course](https://github.com/huggingface/diffusion-models-class)
- [Getting Started with Diffusers](https://huggingface.co/docs/diffusers/index)
- [Unconditional Image Generation Training](https://huggingface.co/docs/diffusers/training/unconditional_training)

### Training your own model in just a few seconds

In this area, you can insert useful information about training the model

This page was made possible thanks to the efforts of [Someet Sahoo](https://huggingface.co/Someet24) and [Juan Carlos Piñeros](https://huggingface.co/juancopi81).
