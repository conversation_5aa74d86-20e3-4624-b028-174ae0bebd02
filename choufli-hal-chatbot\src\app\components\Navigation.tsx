'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useLanguage } from '../contexts/LanguageContext';

export default function Navigation() {
  const pathname = usePathname();
  const { t } = useLanguage();

  const navItems = [
    { href: '/', label: t('nav.home'), icon: '🏠' },
    { href: '/chat', label: t('nav.chat'), icon: '💬' },
    { href: '/login', label: t('nav.login'), icon: '🚪' },
    { href: '/signup', label: t('nav.signup'), icon: '✨' }
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-40 chat-container border-b-2" style={{
      borderColor: 'var(--border-color)',
      backdropFilter: 'blur(10px)'
    }}>
      <div className="max-w-6xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center gap-2 text-xl font-bold" style={{ color: 'var(--choufli-primary)' }}>
            <span>🎭</span>
            <span>{t('home.title')}</span>
          </Link>
          
          <div className="hidden md:flex items-center gap-6">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${
                  pathname === item.href 
                    ? 'font-bold' 
                    : 'hover:opacity-80'
                }`}
                style={{
                  color: pathname === item.href ? 'var(--choufli-accent)' : 'var(--foreground)',
                  backgroundColor: pathname === item.href ? 'rgba(139, 69, 19, 0.1)' : 'transparent'
                }}
              >
                <span>{item.icon}</span>
                <span className="text-sm">{item.label}</span>
              </Link>
            ))}
          </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden">
            <button className="p-2 rounded-lg" style={{ color: 'var(--foreground)' }}>
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}
