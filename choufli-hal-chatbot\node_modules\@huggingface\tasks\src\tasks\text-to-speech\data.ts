import type { TaskDataCustom } from "../index.js";

const taskData: TaskDataCustom = {
	canonicalId: "text-to-audio",
	datasets: [
		{
			description: "10K hours of multi-speaker English dataset.",
			id: "parler-tts/mls_eng_10k",
		},
		{
			description: "Multi-speaker English dataset.",
			id: "mythicinfinity/libritts_r",
		},
		{
			description: "Multi-lingual dataset.",
			id: "facebook/multilingual_librispeech",
		},
	],
	demo: {
		inputs: [
			{
				label: "Input",
				content: "I love audio models on the Hub!",
				type: "text",
			},
		],
		outputs: [
			{
				filename: "audio.wav",
				type: "audio",
			},
		],
	},
	metrics: [
		{
			description: "The Mel Cepstral Distortion (MCD) metric is used to calculate the quality of generated speech.",
			id: "mel cepstral distortion",
		},
	],
	models: [
		{
			description: "A prompt based, powerful TTS model.",
			id: "parler-tts/parler-tts-large-v1",
		},
		{
			description: "A powerful TTS model that supports English and Chinese.",
			id: "SWivid/F5-TTS",
		},
		{
			description: "A massively multi-lingual TTS model.",
			id: "fishaudio/fish-speech-1.5",
		},
		{
			description: "A powerful TTS model.",
			id: "OuteAI/OuteTTS-0.1-350M",
		},
		{
			description: "Small yet powerful TTS model.",
			id: "hexgrad/Kokoro-82M",
		},
	],
	spaces: [
		{
			description: "An application for generate high quality speech in different languages.",
			id: "hexgrad/Kokoro-TTS",
		},
		{
			description: "A multilingual text-to-speech application.",
			id: "fishaudio/fish-speech-1",
		},
		{
			description: "An application that generates speech in different styles in English and Chinese.",
			id: "mrfakename/E2-F5-TTS",
		},
		{
			description: "An application that synthesizes emotional speech for diverse speaker prompts.",
			id: "parler-tts/parler-tts-expresso",
		},
		{
			description: "An application that generates podcast episodes.",
			id: "ngxson/kokoro-podcast-generator",
		},
	],
	summary:
		"Text-to-Speech (TTS) is the task of generating natural sounding speech given text input. TTS models can be extended to have a single model that generates speech for multiple speakers and multiple languages.",
	widgetModels: ["suno/bark"],
	youtubeId: "NW62DpzJ274",
};

export default taskData;
