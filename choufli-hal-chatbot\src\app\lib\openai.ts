import OpenAI from 'openai';
import { CHOUFLI_HAL_SYSTEM_PROMPT } from './prompts';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class ChoufliHalAI {
  private conversationHistory: ChatMessage[] = [];

  constructor() {
    // Initialize with system prompt
    this.conversationHistory = [
      {
        role: 'system',
        content: CHOUFLI_HAL_SYSTEM_PROMPT
      }
    ];
  }

  async generateResponse(userMessage: string): Promise<string> {
    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        content: userMessage
      });

      // Keep conversation history manageable (last 10 messages + system prompt)
      if (this.conversationHistory.length > 21) { // 1 system + 20 messages (10 pairs)
        this.conversationHistory = [
          this.conversationHistory[0], // Keep system prompt
          ...this.conversationHistory.slice(-20) // Keep last 20 messages
        ];
      }

      // Generate response using OpenAI
      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: this.conversationHistory,
        max_tokens: 500,
        temperature: 0.8, // Slightly creative but consistent
        presence_penalty: 0.1,
        frequency_penalty: 0.1,
      });

      const assistantResponse = completion.choices[0]?.message?.content || 
        "أعتذر، لم أستطع فهم رسالتك. هل يمكنك إعادة صياغتها؟ Sorry, I couldn't understand your message. Could you rephrase it?";

      // Add assistant response to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantResponse
      });

      return assistantResponse;

    } catch (error) {
      console.error('OpenAI API Error:', error);
      
      // Fallback responses in case of API error
      const fallbackResponses = [
        "أعتذر، أواجه مشكلة تقنية الآن. Sorry, I'm experiencing technical difficulties right now. Please try again in a moment!",
        "معذرة، الخدمة غير متوفرة حالياً. Excuse me, the service is currently unavailable. But I'm still here to chat when it's back!",
        "الحمد لله، سأعود قريباً! Alhamdulillah, I'll be back soon! There seems to be a temporary issue.",
      ];
      
      return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)];
    }
  }

  // Method to reset conversation (useful for new sessions)
  resetConversation(): void {
    this.conversationHistory = [
      {
        role: 'system',
        content: CHOUFLI_HAL_SYSTEM_PROMPT
      }
    ];
  }

  // Method to get conversation history (useful for debugging)
  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }
}

// Create a singleton instance
export const choufliHalAI = new ChoufliHalAI();
