import type { TextToSpeechInput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
type TextToSpeechArgs = BaseArgs & TextToSpeechInput;
/**
 * This task synthesize an audio of a voice pronouncing a given text.
 * Recommended model: espnet/kan-bayashi_ljspeech_vits
 */
export declare function textToSpeech(args: TextToSpeechArgs, options?: Options): Promise<Blob>;
export {};
//# sourceMappingURL=textToSpeech.d.ts.map