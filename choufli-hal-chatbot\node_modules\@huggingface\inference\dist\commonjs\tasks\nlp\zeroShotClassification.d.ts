import type { ZeroShotClassificationInput, ZeroShotClassificationOutput } from "@huggingface/tasks";
import type { BaseArgs, Options } from "../../types.js";
export type ZeroShotClassificationArgs = BaseArgs & ZeroShotClassificationInput;
/**
 * This task is super useful to try out classification with zero code, you simply pass a sentence/paragraph and the possible labels for that sentence, and you get a result. Recommended model: facebook/bart-large-mnli.
 */
export declare function zeroShotClassification(args: ZeroShotClassificationArgs, options?: Options): Promise<ZeroShotClassificationOutput>;
//# sourceMappingURL=zeroShotClassification.d.ts.map