{"$id": "/inference/schemas/token-classification/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Token Classification inference", "title": "TokenClassificationInput", "type": "object", "properties": {"inputs": {"description": "The input text data", "type": "string"}, "parameters": {"description": "Additional inference parameters for Token Classification", "$ref": "#/$defs/TokenClassificationParameters"}}, "$defs": {"TokenClassificationParameters": {"title": "TokenClassificationParameters", "type": "object", "properties": {"ignore_labels": {"type": "array", "items": {"type": "string"}, "description": "A list of labels to ignore"}, "stride": {"type": "integer", "description": "The number of overlapping tokens between chunks when splitting the input text."}, "aggregation_strategy": {"title": "TokenClassificationAggregationStrategy", "type": "string", "description": "The strategy used to fuse tokens based on model predictions", "oneOf": [{"const": "none", "description": "Do not aggregate tokens"}, {"const": "simple", "description": "Group consecutive tokens with the same label in a single entity."}, {"const": "first", "description": "Similar to \"simple\", also preserves word integrity (use the label predicted for the first token in a word)."}, {"const": "average", "description": "Similar to \"simple\", also preserves word integrity (uses the label with the highest score, averaged across the word's tokens)."}, {"const": "max", "description": "Similar to \"simple\", also preserves word integrity (uses the label with the highest score across the word's tokens)."}]}}}}, "required": ["inputs"]}