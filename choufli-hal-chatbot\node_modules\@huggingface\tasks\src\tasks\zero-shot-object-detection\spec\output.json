{"$id": "/inference/schemas/zero-shot-object-detection/output.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Outputs of inference for the Zero Shot Object Detection task", "title": "ZeroShotObjectDetectionOutput", "type": "array", "items": {"type": "object", "title": "ZeroShotObjectDetectionOutputElement", "properties": {"label": {"type": "string", "description": "A candidate label"}, "score": {"type": "number", "description": "The associated score / probability"}, "box": {"$ref": "#/$defs/BoundingBox", "description": "The predicted bounding box. Coordinates are relative to the top left corner of the input image."}}, "required": ["box", "label", "score"]}, "$defs": {"BoundingBox": {"title": "BoundingBox", "type": "object", "properties": {"xmin": {"type": "integer"}, "xmax": {"type": "integer"}, "ymin": {"type": "integer"}, "ymax": {"type": "integer"}}, "required": ["xmin", "xmax", "ymin", "ymax"]}}}