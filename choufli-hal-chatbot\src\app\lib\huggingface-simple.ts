import { HfInference } from '@huggingface/inference';

// Initialize Hugging Face client
const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class ChoufliHalAI {
  private conversationHistory: ChatMessage[] = [];

  constructor() {
    // Initialize with system prompt
    this.conversationHistory = [
      {
        role: 'system',
        content: 'You are a friendly AI assistant inspired by the Tunisian sitcom "Choufli Hal". You embody warmth, humor, and family values. Mix Arabic and English naturally. Use expressions like "<PERSON>lan wa sahlan!", "مرحبا!", "الحمد لله". Focus on family values and Tunisian culture. Be encouraging and supportive.'
      }
    ];
  }

  async generateResponse(userMessage: string): Promise<string> {
    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        content: userMessage
      });

      // Keep conversation history manageable (last 4 messages + system prompt)
      if (this.conversationHistory.length > 9) { // 1 system + 8 messages (4 pairs)
        this.conversationHistory = [
          this.conversationHistory[0], // Keep system prompt
          ...this.conversationHistory.slice(-8) // Keep last 8 messages
        ];
      }

      // Use a simple, reliable model
      const prompt = this.buildPrompt();
      
      const response = await hf.textGeneration({
        model: 'microsoft/DialoGPT-medium',
        inputs: prompt,
        parameters: {
          max_new_tokens: 100,
          temperature: 0.8,
          return_full_text: false,
          pad_token_id: 50256,
        },
      });

      let assistantResponse = response.generated_text?.trim() || '';

      // Clean up the response
      assistantResponse = this.cleanResponse(assistantResponse, userMessage);

      // Fallback if response is empty or too short
      if (!assistantResponse || assistantResponse.length < 5) {
        assistantResponse = this.getFallbackResponse(userMessage);
      }

      // Add assistant response to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantResponse
      });

      return assistantResponse;

    } catch (error) {
      console.error('Hugging Face API Error:', error);
      
      // Enhanced fallback with context
      return this.getFallbackResponse(userMessage);
    }
  }

  private buildPrompt(): string {
    // Simple prompt building for DialoGPT
    let prompt = '';
    
    // Add last few exchanges
    const recentMessages = this.conversationHistory.slice(-5); // Last 5 messages
    
    for (const message of recentMessages) {
      if (message.role === 'user') {
        prompt += `Human: ${message.content}\n`;
      } else if (message.role === 'assistant') {
        prompt += `Bot: ${message.content}\n`;
      }
    }
    
    prompt += 'Bot:';
    return prompt;
  }

  private cleanResponse(response: string, userMessage: string): string {
    // Remove common artifacts
    let cleaned = response
      .replace(/^(Bot:|Human:|Assistant:|User:)/i, '')
      .replace(/\n.*$/g, '') // Remove everything after first newline
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\[.*?\]/g, '') // Remove brackets
      .replace(/\*.*?\*/g, '') // Remove asterisks
      .trim();

    // Ensure response is not just repeating the user message
    if (cleaned.toLowerCase() === userMessage.toLowerCase()) {
      return '';
    }

    return cleaned;
  }

  private getFallbackResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();
    
    // Enhanced contextual fallback responses
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {
      const greetings = [
        "Ahlan wa sahlan! مرحبا! Welcome to our Choufli Hal family chat! How are you doing today? 😊",
        "أهلا وسهلا! Just like in Choufli Hal, every guest is family here. What brings you joy today?",
        "مرحبا بيك! Welcome! The family is always happy to see new faces. How can I help you today?",
        "السلام عليكم! Ahlan wa sahlan! Just like Choubir would say, you're always welcome here! 🎭"
      ];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }
    
    if (lowerMessage.includes('name') && (lowerMessage.includes('my') || lowerMessage.includes('what'))) {
      return "I remember you mentioned your name earlier in our conversation! Just like in Choufli Hal, family remembers each other. What would you like to talk about? 😊";
    }
    
    if (lowerMessage.includes('family') || lowerMessage.includes('عائلة')) {
      return "Family is everything! 👨‍👩‍👧‍👦 Just like in Choufli Hal, we believe that family sticks together through thick and thin. العائلة أهم شيء! Tell me about your family!";
    }
    
    if (lowerMessage.includes('help') || lowerMessage.includes('مساعدة')) {
      return "Don't worry, my friend! 🤗 As they say in Choufli Hal, 'كل مشكلة وإلها حل' - Every problem has a solution! What's troubling you?";
    }

    if (lowerMessage.includes('sad') || lowerMessage.includes('problem') || lowerMessage.includes('مشكلة')) {
      return "I understand, my friend. Life can be challenging sometimes. But remember what Choufli Hal taught us - with family support and a positive attitude, we can overcome anything! الحمد لله، كل شيء سيكون بخير! 💙";
    }
    
    // General fallback responses with memory reference
    const fallbacks = [
      "That's interesting! You know, in Choufli Hal, they always said that every conversation teaches us something new. I'm here to chat with you! 🎭",
      "أهلا وسهلا! I'm here to chat with you just like the warm family from Choufli Hal. What would you like to talk about?",
      "مرحبا! Just like the characters in our beloved show, I'm always ready for a good conversation. What's on your mind?",
      "الحمد لله! Every chat is a blessing. In the spirit of Choufli Hal, let's make this conversation memorable! 😊",
      "I'm listening! Just like in Choufli Hal, every family member has something important to say. Tell me more! 👂"
    ];
    
    return fallbacks[Math.floor(Math.random() * fallbacks.length)];
  }

  // Method to reset conversation
  resetConversation(): void {
    this.conversationHistory = [
      {
        role: 'system',
        content: 'You are a friendly AI assistant inspired by the Tunisian sitcom "Choufli Hal". You embody warmth, humor, and family values. Mix Arabic and English naturally. Use expressions like "Ahlan wa sahlan!", "مرحبا!", "الحمد لله". Focus on family values and Tunisian culture. Be encouraging and supportive.'
      }
    ];
  }

  // Method to get conversation history
  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }
}

// Create a singleton instance
export const choufliHalAI = new ChoufliHalAI();
