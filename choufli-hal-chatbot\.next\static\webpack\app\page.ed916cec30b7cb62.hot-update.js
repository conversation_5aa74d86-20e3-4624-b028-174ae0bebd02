"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/components/ChoufliCharacters.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/ChoufliCharacters.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChoufliCharacters)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nconst choufliCharacters = [\n    {\n        name: \"Choubir\",\n        nameArabic: \"شبير\",\n        emoji: \"👨‍🔧\",\n        description: \"The main character, a simple and honest man who works hard to support his family\",\n        quote: \"الحمد لله على كل حال - Thank God for everything\",\n        color: \"var(--choufli-primary)\"\n    },\n    {\n        name: \"Najet\",\n        nameArabic: \"ناجت\",\n        emoji: \"👩‍🏠\",\n        description: \"Choubir's wife, a devoted mother and housewife who manages the household\",\n        quote: \"الصبر مفتاح الفرج - Patience is the key to relief\",\n        color: \"var(--choufli-secondary)\"\n    },\n    {\n        name: \"Slah\",\n        nameArabic: \"صلاح\",\n        emoji: \"👨‍🎓\",\n        description: \"Choubir's eldest son, a university student with big dreams and ambitions\",\n        quote: \"العلم نور والجهل ظلام - Knowledge is light and ignorance is darkness\",\n        color: \"var(--choufli-accent)\"\n    },\n    {\n        name: \"Najla\",\n        nameArabic: \"نجلة\",\n        emoji: \"👩‍🎨\",\n        description: \"Choubir's daughter, a creative and modern young woman\",\n        quote: \"المرأة نصف المجتمع - Women are half of society\",\n        color: \"var(--choufli-warm)\"\n    },\n    {\n        name: \"Hamma\",\n        nameArabic: \"حمة\",\n        emoji: \"👨‍🦳\",\n        description: \"Choubir's father, the wise patriarch of the family with traditional values\",\n        quote: \"احترم كبيرك يحترمك صغيرك - Respect your elders and the young will respect you\",\n        color: \"var(--choufli-gold)\"\n    },\n    {\n        name: \"Ommi Kalthoum\",\n        nameArabic: \"أمي كلثوم\",\n        emoji: \"👵\",\n        description: \"Choubir's mother, the loving grandmother who holds the family together\",\n        quote: \"البيت بلا أم كالجسم بلا روح - A home without a mother is like a body without a soul\",\n        color: \"var(--choufli-deep)\"\n    },\n    {\n        name: \"Hedi\",\n        nameArabic: \"هادي\",\n        emoji: \"👦\",\n        description: \"Choubir's youngest son, a mischievous but lovable child\",\n        quote: \"اللعب حق الطفل - Playing is a child's right\",\n        color: \"var(--choufli-neon)\"\n    },\n    {\n        name: \"Hajja Zeineb\",\n        nameArabic: \"الحاجة زينب\",\n        emoji: \"👩‍🦳\",\n        description: \"The nosy but well-meaning neighbor who always has something to say\",\n        quote: \"الجار قبل الدار - The neighbor comes before the house\",\n        color: \"var(--choufli-cosmic)\"\n    }\n];\nfunction ChoufliCharacters() {\n    _s();\n    const [selectedCharacter, setSelectedCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-bold text-center mb-8\",\n                style: {\n                    color: 'var(--choufli-primary)'\n                },\n                children: \"Meet the Choufli Hal Family / تعرف على عائلة شوفلي حل\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-8\",\n                children: choufliCharacters.map((character, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"chat-container p-4 rounded-xl text-center cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl\",\n                        onClick: ()=>setSelectedCharacter(character),\n                        style: {\n                            borderColor: character.color,\n                            borderWidth: '2px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl mb-2\",\n                                children: character.emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-bold text-sm\",\n                                style: {\n                                    color: character.color\n                                },\n                                children: character.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs opacity-75\",\n                                style: {\n                                    color: 'var(--foreground)'\n                                },\n                                children: character.nameArabic\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            selectedCharacter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"chat-container p-6 rounded-2xl shadow-2xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl\",\n                                children: selectedCharacter.emoji\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-bold\",\n                                        style: {\n                                            color: selectedCharacter.color\n                                        },\n                                        children: selectedCharacter.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg opacity-75\",\n                                        style: {\n                                            color: 'var(--foreground)'\n                                        },\n                                        children: selectedCharacter.nameArabic\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-4\",\n                        style: {\n                            color: 'var(--foreground)'\n                        },\n                        children: selectedCharacter.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-xl\",\n                        style: {\n                            backgroundColor: 'rgba(139, 69, 19, 0.1)',\n                            borderLeft: \"4px solid \".concat(selectedCharacter.color)\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium italic\",\n                            style: {\n                                color: selectedCharacter.color\n                            },\n                            children: [\n                                '\"',\n                                selectedCharacter.quote,\n                                '\"'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setSelectedCharacter(null),\n                        className: \"mt-4 px-4 py-2 rounded-lg text-white transition-all duration-300 hover:scale-105\",\n                        style: {\n                            background: selectedCharacter.color\n                        },\n                        children: \"Close / إغلاق\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Bureau\\\\static bot choufli 7al\\\\choufli-hal-chatbot\\\\src\\\\app\\\\components\\\\ChoufliCharacters.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(ChoufliCharacters, \"Qx6Sx7sB8Jlbq2wHCIDog9u+zk4=\");\n_c = ChoufliCharacters;\nvar _c;\n$RefreshReg$(_c, \"ChoufliCharacters\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/components/ChoufliCharacters.tsx\n"));

/***/ })

});