'use client';

import { useLanguage } from '../contexts/LanguageContext';

export default function LanguageToggle() {
  const { language, toggleLanguage } = useLanguage();

  return (
    <button
      onClick={toggleLanguage}
      className="fixed top-6 left-6 z-50 p-3 rounded-full text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      title={`Switch to ${language === 'en' ? 'Arabic' : 'English'}`}
      style={{
        background: `linear-gradient(135deg, var(--choufli-primary), var(--choufli-secondary))`
      }}
    >
      <div className="flex items-center gap-2 font-bold">
        {language === 'en' ? (
          <>
            <span>🇹🇳</span>
            <span>AR</span>
          </>
        ) : (
          <>
            <span>🇺🇸</span>
            <span>EN</span>
          </>
        )}
      </div>
    </button>
  );
}
