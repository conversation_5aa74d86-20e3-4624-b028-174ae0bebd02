import { HfInference } from '@huggingface/inference';
import { CHOUFLI_HAL_SYSTEM_PROMPT } from './prompts';

// Initialize Hugging Face client
const hf = new HfInference(process.env.HUGGINGFACE_API_KEY);

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export class ChoufliHalAI {
  private conversationHistory: ChatMessage[] = [];
  private readonly model = 'microsoft/DialoGPT-medium'; // Good for conversations
  // Alternative models you can try:
  // 'meta-llama/Llama-2-7b-chat-hf' - More advanced but might be slower
  // 'mistralai/Mistral-7B-Instruct-v0.1' - Good balance
  // 'microsoft/DialoGPT-medium' - Fast and good for chat

  constructor() {
    // Initialize with system prompt
    this.conversationHistory = [
      {
        role: 'system',
        content: CHOUFLI_HAL_SYSTEM_PROMPT
      }
    ];
  }

  async generateResponse(userMessage: string): Promise<string> {
    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        content: userMessage
      });

      // Keep conversation history manageable (last 8 messages + system prompt)
      if (this.conversationHistory.length > 17) { // 1 system + 16 messages (8 pairs)
        this.conversationHistory = [
          this.conversationHistory[0], // Keep system prompt
          ...this.conversationHistory.slice(-16) // Keep last 16 messages
        ];
      }

      // Format conversation for Hugging Face
      const conversationText = this.formatConversationForHF();

      // Generate response using Hugging Face
      const response = await hf.textGeneration({
        model: this.model,
        inputs: conversationText,
        parameters: {
          max_new_tokens: 200,
          temperature: 0.8,
          top_p: 0.9,
          repetition_penalty: 1.1,
          return_full_text: false,
        },
      });

      let assistantResponse = response.generated_text?.trim() || '';

      // Clean up the response
      assistantResponse = this.cleanResponse(assistantResponse, userMessage);

      // Fallback if response is empty or too short
      if (!assistantResponse || assistantResponse.length < 10) {
        assistantResponse = this.getFallbackResponse(userMessage);
      }

      // Add assistant response to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: assistantResponse
      });

      return assistantResponse;

    } catch (error) {
      console.error('Hugging Face API Error:', error);
      
      // Fallback responses in case of API error
      return this.getFallbackResponse(userMessage);
    }
  }

  private formatConversationForHF(): string {
    // Format the conversation in a way that works well with Hugging Face models
    let conversation = `${CHOUFLI_HAL_SYSTEM_PROMPT}\n\n`;
    
    // Add recent conversation history (excluding system prompt)
    const recentMessages = this.conversationHistory.slice(1, -1); // Exclude system and current user message
    
    for (const message of recentMessages) {
      if (message.role === 'user') {
        conversation += `Human: ${message.content}\n`;
      } else if (message.role === 'assistant') {
        conversation += `Assistant: ${message.content}\n`;
      }
    }
    
    // Add current user message
    const currentUserMessage = this.conversationHistory[this.conversationHistory.length - 1];
    conversation += `Human: ${currentUserMessage.content}\nAssistant:`;
    
    return conversation;
  }

  private cleanResponse(response: string, userMessage: string): string {
    // Remove common artifacts from generated text
    let cleaned = response
      .replace(/^(Assistant:|Human:|User:)/i, '') // Remove role prefixes
      .replace(/\n\n+/g, '\n') // Remove excessive newlines
      .replace(/^\s+|\s+$/g, '') // Trim whitespace
      .replace(/\[.*?\]/g, '') // Remove brackets
      .replace(/\*.*?\*/g, '') // Remove asterisks
      .replace(/Human:.*$/i, '') // Remove any trailing human input
      .trim();

    // Ensure response is not just repeating the user message
    if (cleaned.toLowerCase().includes(userMessage.toLowerCase()) && cleaned.length < userMessage.length + 20) {
      return this.getFallbackResponse(userMessage);
    }

    return cleaned;
  }

  private getFallbackResponse(userMessage: string): string {
    const lowerMessage = userMessage.toLowerCase();
    
    // Contextual fallback responses based on message content
    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('مرحبا') || lowerMessage.includes('أهلا')) {
      const greetings = [
        "Ahlan wa sahlan! مرحبا! Welcome to our Choufli Hal family chat! How are you doing today? 😊",
        "أهلا وسهلا! Just like in Choufli Hal, every guest is family here. What brings you joy today?",
        "مرحبا بيك! Welcome! The family is always happy to see new faces. How can I help you today?"
      ];
      return greetings[Math.floor(Math.random() * greetings.length)];
    }
    
    if (lowerMessage.includes('family') || lowerMessage.includes('عائلة')) {
      return "Family is everything! 👨‍👩‍👧‍👦 Just like in Choufli Hal, we believe that family sticks together through thick and thin. Tell me about your family!";
    }
    
    if (lowerMessage.includes('help') || lowerMessage.includes('مساعدة')) {
      return "Don't worry, my friend! 🤗 As they say in Choufli Hal, 'كل مشكلة وإلها حل' - Every problem has a solution! What's troubling you?";
    }
    
    // General fallback responses
    const fallbacks = [
      "That's interesting! You know, in Choufli Hal, they always said that every conversation teaches us something new. Tell me more! 🎭",
      "أهلا وسهلا! I'm here to chat with you just like the warm family from Choufli Hal. What would you like to talk about?",
      "مرحبا! Just like the characters in our beloved show, I'm always ready for a good conversation. What's on your mind?",
      "الحمد لله! Every chat is a blessing. In the spirit of Choufli Hal, let's make this conversation memorable! 😊"
    ];
    
    return fallbacks[Math.floor(Math.random() * fallbacks.length)];
  }

  // Method to reset conversation (useful for new sessions)
  resetConversation(): void {
    this.conversationHistory = [
      {
        role: 'system',
        content: CHOUFLI_HAL_SYSTEM_PROMPT
      }
    ];
  }

  // Method to get conversation history (useful for debugging)
  getConversationHistory(): ChatMessage[] {
    return [...this.conversationHistory];
  }
}

// Create a singleton instance
export const choufliHalAI = new ChoufliHalAI();
