/**
 * Elements configurable by a dataset library.
 */
export interface DatasetLibraryUiElement {
    /**
     * Pretty name of the library.
     * displayed (in tags?, and) on the main
     * call-to-action button on the dataset page.
     */
    prettyLabel: string;
    /**
     * Repo name of the library's (usually on GitHub) code repo
     */
    repoName: string;
    /**
     * URL to library's (usually on GitHub) code repo
     */
    repoUrl: string;
    /**
     * URL to library's docs
     */
    docsUrl?: string;
}
export declare const DATASET_LIBRARIES_UI_ELEMENTS: {
    mlcroissant: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    webdataset: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    datasets: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    pandas: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    dask: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    distilabel: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    fiftyone: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    argilla: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    polars: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
    duckdb: {
        prettyLabel: string;
        repoName: string;
        repoUrl: string;
        docsUrl: string;
    };
};
export type DatasetLibraryKey = keyof typeof DATASET_LIBRARIES_UI_ELEMENTS;
//# sourceMappingURL=dataset-libraries.d.ts.map