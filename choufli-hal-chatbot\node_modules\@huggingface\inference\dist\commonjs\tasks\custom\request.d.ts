import type { InferenceTask, Options, RequestArgs } from "../../types.js";
/**
 * Primitive to make custom calls to the inference provider
 * @deprecated Use specific task functions instead. This function will be removed in a future version.
 */
export declare function request<T>(args: RequestArgs, options?: Options & {
    /** In most cases (unless we pass a endpointUrl) we know the task */
    task?: InferenceTask;
}): Promise<T>;
//# sourceMappingURL=request.d.ts.map