{"$id": "/inference/schemas/video-classification/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Video Classification inference", "title": "VideoClassificationInput", "type": "object", "properties": {"inputs": {"description": "The input video data"}, "parameters": {"description": "Additional inference parameters for Video Classification", "$ref": "#/$defs/VideoClassificationParameters"}}, "$defs": {"VideoClassificationParameters": {"title": "VideoClassificationParameters", "type": "object", "properties": {"function_to_apply": {"title": "TextClassificationOutputTransform", "$ref": "/inference/schemas/common-definitions.json#/definitions/ClassificationOutputTransform", "description": "The function to apply to the model outputs in order to retrieve the scores."}, "num_frames": {"type": "integer", "description": "The number of sampled frames to consider for classification."}, "frame_sampling_rate": {"type": "integer", "description": "The sampling rate used to select frames from the video."}, "top_k": {"type": "integer", "description": "When specified, limits the output to the top K most probable classes."}}}}, "required": ["inputs"]}