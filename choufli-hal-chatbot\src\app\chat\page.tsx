'use client';

import { useState, useRef, useEffect } from 'react';
import ThemeToggle from '../components/ThemeToggle';
import LanguageToggle from '../components/LanguageToggle';
import Navigation from '../components/Navigation';
import ChatStatus from '../components/ChatStatus';
import { useLanguage } from '../contexts/LanguageContext';
import { getRandomStarter } from '../lib/prompts';
import Link from 'next/link';

interface Message {
  id: number;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function ChatPage() {
  const { t } = useLanguage();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: getRandomStarter(),
      isUser: false,
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now(),
      text: inputMessage,
      isUser: true,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: inputMessage }),
      });

      const data = await response.json();

      if (response.ok) {
        const botMessage: Message = {
          id: Date.now() + 1,
          text: data.response,
          isUser: false,
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, botMessage]);
      } else {
        throw new Error(data.error || 'Failed to get response');
      }
    } catch (error) {
      const errorMessage: Message = {
        id: Date.now() + 1,
        text: 'Sorry, I encountered an error. Please try again!',
        isUser: false,
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen choufli-background">
      <Navigation />
      <ThemeToggle />
      <LanguageToggle />
      
      {/* Header */}
      <div className="text-white p-6 shadow-2xl header-choufli mt-16" style={{
        background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
      }}>
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <Link 
              href="/"
              className="text-white hover:text-amber-200 transition-colors duration-300"
              title="Back to Home"
            >
              <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </Link>
            
            <div className="text-center flex-1">
              <h1 className="text-4xl font-bold text-glow">
                <span className="emoji-bounce">🎭</span> {t('chat.title')} <span className="emoji-bounce">🎭</span>
              </h1>
              <p className="mt-2 text-amber-100 text-lg font-medium">
                {t('chat.subtitle')}
              </p>
            </div>
            
            <div className="w-8"></div> {/* Spacer for centering */}
          </div>
          <div className="text-center mt-2">
            <span className="text-amber-200 text-sm">3aslema mar7be bik fi choufli7al chat manetsawarech de5el ll ta9ti3 w taryych ma3neha</span>
          </div>
          <div className="text-center mt-4">
            <ChatStatus />
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className="max-w-4xl mx-auto p-6 h-[calc(100vh-160px)] flex flex-col">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto mb-6 chat-container rounded-xl shadow-2xl p-6 scroll-smooth">
          <div className="space-y-6">
            {messages.map((message, index) => (
              <div
                key={message.id}
                className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} message-container`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div
                  className={`max-w-[75%] p-4 rounded-2xl shadow-lg transform transition-all duration-300 hover:scale-105 ${
                    message.isUser
                      ? 'bg-gradient-to-br from-slate-600 via-slate-700 to-slate-800 text-white message-user'
                      : 'text-white message-bot'
                  }`}
                  style={!message.isUser ? {
                    background: `linear-gradient(to bottom right, var(--choufli-secondary), var(--choufli-warm), var(--choufli-accent))`
                  } : {}}
                >
                  <p className="text-sm font-medium leading-relaxed">{message.text}</p>
                  <p className="text-xs mt-2 opacity-80 flex items-center gap-1">
                    <span>{message.isUser ? '👤' : '🤖'}</span>
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start message-container">
                <div className="text-white p-4 rounded-2xl shadow-lg" style={{
                  background: `linear-gradient(to bottom right, var(--choufli-secondary), var(--choufli-warm), var(--choufli-accent))`
                }}>
                  <div className="flex items-center gap-2">
                    <span className="text-sm">🤖 typing...</span>
                    <div className="loading-dots">
                      <div className="loading-dot"></div>
                      <div className="loading-dot"></div>
                      <div className="loading-dot"></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="chat-container rounded-xl shadow-2xl p-6">
          <div className="flex space-x-4">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage();
                }
              }}
              placeholder={t('chat.placeholder')}
              className="flex-1 p-4 border-3 rounded-xl focus:outline-none focus:ring-4 focus:ring-opacity-50 font-medium text-lg input-choufli"
              disabled={isLoading}
              style={{
                borderColor: 'var(--border-color)',
                backgroundColor: 'var(--card-bg)',
                color: 'var(--foreground)'
              }}
            />
            <button
              onClick={sendMessage}
              disabled={isLoading || !inputMessage.trim()}
              className="text-white px-8 py-4 rounded-xl font-bold disabled:opacity-50 disabled:cursor-not-allowed btn-choufli shadow-xl text-lg transition-all duration-300 hover:scale-105"
              style={{
                background: `linear-gradient(to right, var(--choufli-primary), var(--choufli-secondary), var(--choufli-accent))`
              }}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="loading-dots">
                    <div className="loading-dot"></div>
                    <div className="loading-dot"></div>
                    <div className="loading-dot"></div>
                  </div>
                </div>
              ) : (
                <span className="flex items-center gap-2">
                  <span>{t('chat.send')}</span>
                  <span>📤</span>
                </span>
              )}
            </button>
          </div>
          <div className="mt-3 text-center">
            <p className="text-sm" style={{ color: 'var(--foreground)' }}>
              <span className="emoji-bounce">✨</span>
              {t('chat.pressEnter')}
              <span className="emoji-bounce">✨</span>
            </p>
          </div>
        </div>
      </div>

      {/* Choufli 7al Character Decoration */}
      <div
        className="choufli-character"
        title="Choubir says hello! 👋"
        onClick={() => {
          const messages = [
            "مرحبا! أهلا وسهلا! Choubir here!",
            "كيف الحال؟ شوفلي حل! How are you?",
            "الحمد لله، كلشي بخير! Everything is good!",
            "يا أهلا وسهلا بيك! Welcome to our chat!"
          ];
          const randomMessage = messages[Math.floor(Math.random() * messages.length)];
          alert(randomMessage);
        }}
      >
        👨‍🔧
      </div>
    </div>
  );
}
