{"$id": "/inference/schemas/text-to-video/input.json", "$schema": "http://json-schema.org/draft-06/schema#", "description": "Inputs for Text To Video inference", "title": "TextToVideoInput", "type": "object", "properties": {"inputs": {"description": "The input text data (sometimes called \"prompt\")", "type": "string"}, "parameters": {"description": "Additional inference parameters for Text To Video", "$ref": "#/$defs/TextToVideoParameters"}}, "$defs": {"TextToVideoParameters": {"title": "TextToVideoParameters", "type": "object", "properties": {"num_frames": {"type": "number", "description": "The num_frames parameter determines how many video frames are generated."}, "guidance_scale": {"type": "number", "description": "A higher guidance scale value encourages the model to generate videos closely linked to the text prompt, but values too high may cause saturation and other artifacts."}, "negative_prompt": {"type": "array", "items": {"type": "string"}, "description": "One or several prompt to guide what NOT to include in video generation."}, "num_inference_steps": {"type": "integer", "description": "The number of denoising steps. More denoising steps usually lead to a higher quality video at the expense of slower inference."}, "seed": {"type": "integer", "description": "Seed for the random number generator."}}}}, "required": ["inputs"]}